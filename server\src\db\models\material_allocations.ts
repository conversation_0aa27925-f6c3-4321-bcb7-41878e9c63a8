import { ColumnType, Generated } from 'kysely'

// Material Allocations table
export interface MaterialAllocationsTable {
  id: Generated<number>
  poly_raw_allocation_id: number
  poly_manufacture_id: number
  num: string
  mo_status: string
  part_number: string
  poly_raw_material_id: number
  part_color: string
  poly_component_id: number
  component_name: string
  category_name: string
  sub_category_name: string
  unit_symbol: string
  quantity_on_hand: number
  quantity_ordered: number
  quantity_allocated: number
  quantity_required: number
  quantity_adjust: number
  quantity_withdrawn: number
  order_delivery_date: ColumnType<Date, string | undefined, string | undefined> | null
  style_number: string
  style_id: number
  style_color: string
  customer: string
  stock_warehouse_id: number
  company_code: number
}
