import React from 'react'

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean
  children: React.ReactNode
}

export const Label: React.FC<LabelProps> = ({
  required = false,
  children,
  className = '',
  ...props
}) => {
  const classes = `block text-sm font-semibold text-gray-700 mb-1 ${className}`
  
  return (
    <label className={classes} {...props}>
      {children}
      {required && <span className="text-danger-500 ml-1">*</span>}
    </label>
  )
}
