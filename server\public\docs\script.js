// Theme management
function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute('data-theme')
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark'

  document.documentElement.setAttribute('data-theme', newTheme)
  localStorage.setItem('theme', newTheme)

  // Update theme icon
  const themeIcon = document.querySelector('.theme-icon')
  themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙'
}

// Initialize theme
function initializeTheme() {
  const savedTheme = localStorage.getItem('theme') || 'light'
  document.documentElement.setAttribute('data-theme', savedTheme)

  // Update theme icon
  const themeIcon = document.querySelector('.theme-icon')
  if (themeIcon) {
    themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙'
  }
}

// Child attributes toggle functionality
function toggleChildAttributes(button) {
  const section = button.closest('.child-attributes-section')
  const content = section.querySelector('.child-attributes-content')
  const originalButton = section.querySelector(
    '.child-attributes-header .child-attributes-toggle:not(.inside-box)'
  )

  if (content) {
    const isExpanded = content.classList.contains('expanded')

    if (isExpanded) {
      // Collapsing - restore original structure and show original button
      content.classList.remove('expanded')

      // Restore original content structure
      const expandedContent = content.querySelector(
        '.child-attributes-expanded-content'
      )
      if (expandedContent) {
        const originalContent = expandedContent.innerHTML
        content.innerHTML = originalContent
      }

      // Show the original button again
      if (originalButton) {
        originalButton.style.display = 'flex'
        originalButton.classList.remove('expanded')
        const toggleText = originalButton.querySelector('.toggle-text')
        const toggleIcon = originalButton.querySelector('.toggle-icon')
        if (toggleText) toggleText.textContent = 'Show child attributes'
        if (toggleIcon) toggleIcon.textContent = '+'
      }
    } else {
      // Expanding - hide original button and create new structure
      content.classList.add('expanded')

      // Hide the original button
      if (originalButton) {
        originalButton.style.display = 'none'
        originalButton.classList.add('expanded')
      }

      // Store original content
      const originalContent = content.innerHTML

      // Create new structure with hide button in header
      content.innerHTML = `
        <div class="child-attributes-expanded-header">
          <span class="toggle-text">Hide child attributes</span>
          <span class="toggle-icon">−</span>
        </div>
        <div class="child-attributes-expanded-content">
          ${originalContent}
        </div>
      `

      // Event listeners are handled by event delegation, no need to add individual listeners
    }
  }
}

// Initialize child attributes toggles using event delegation
function initializeChildAttributesToggle() {
  // Use event delegation to handle both initial and dynamically created buttons
  document.addEventListener('click', function (event) {
    // Check if the clicked element is a child attributes toggle button
    if (event.target.closest('.child-attributes-toggle')) {
      const toggle = event.target.closest('.child-attributes-toggle')
      // Prevent event bubbling to avoid conflicts with header clicks
      event.stopPropagation()
      toggleChildAttributes(toggle)
    }
    // Check if the clicked element is an expanded header
    else if (event.target.closest('.child-attributes-expanded-header')) {
      const header = event.target.closest('.child-attributes-expanded-header')
      const section = header.closest('.child-attributes-section')
      const originalButton = section.querySelector(
        '.child-attributes-header .child-attributes-toggle:not(.inside-box)'
      )
      if (originalButton) {
        toggleChildAttributes(originalButton)
      }
    }
  })
}

// Password protection configuration
const SECTION_PASSWORDS = {
  BsnDocs: 'bsn-protected',
}

// Password protection functionality
function unlockSections() {
  const passwordInput = document.getElementById('sectionPassword')
  const loginStatus = document.getElementById('loginStatus')
  const password = passwordInput.value.trim()

  if (!password) {
    showLoginStatus('Please enter a password', 'error')
    return
  }

  const sectionClass = SECTION_PASSWORDS[password]

  if (sectionClass) {
    // Unlock the specific section
    const protectedElements = document.querySelectorAll(`.${sectionClass}`)
    protectedElements.forEach((element) => {
      element.style.display = 'block'
      element.classList.add('unlocked')
    })

    showLoginStatus(
      `${getSectionName(sectionClass)} section unlocked!`,
      'success'
    )
    passwordInput.value = ''

    // Store unlocked sections in sessionStorage
    const unlockedSections = JSON.parse(
      sessionStorage.getItem('unlockedSections') || '[]'
    )
    if (!unlockedSections.includes(sectionClass)) {
      unlockedSections.push(sectionClass)
      sessionStorage.setItem(
        'unlockedSections',
        JSON.stringify(unlockedSections)
      )
    }
  } else {
    showLoginStatus('Invalid password', 'error')
    passwordInput.value = ''
  }
}

function getSectionName(sectionClass) {
  const sectionNames = {
    'bsn-protected': 'BSN Sports Integration',
  }
  return sectionNames[sectionClass] || 'Protected'
}

function showLoginStatus(message, type) {
  const loginStatus = document.getElementById('loginStatus')
  loginStatus.textContent = message
  loginStatus.className = `login-status ${type}`

  setTimeout(() => {
    loginStatus.textContent = ''
    loginStatus.className = 'login-status'
  }, 3000)
}

// Restore unlocked sections on page load
function restoreUnlockedSections() {
  const unlockedSections = JSON.parse(
    sessionStorage.getItem('unlockedSections') || '[]'
  )

  unlockedSections.forEach((sectionClass) => {
    const protectedElements = document.querySelectorAll(`.${sectionClass}`)
    protectedElements.forEach((element) => {
      element.style.display = 'block'
      element.classList.add('unlocked')
    })
  })
}

// Handle Enter key in password input
function setupPasswordInput() {
  const passwordInput = document.getElementById('sectionPassword')
  passwordInput.addEventListener('keypress', function (e) {
    if (e.key === 'Enter') {
      unlockSections()
    }
  })
}

// Section sub-navigation mapping
const SECTION_SUBNAV = {
  'order-endpoints': ['order-response-schema', 'order-details'],
  'bsn-endpoints': ['bsn-po-submit'],
}

// Navigation functionality for the documentation
document.addEventListener('DOMContentLoaded', function () {
  // Initialize theme
  initializeTheme()

  // Initialize password protection
  restoreUnlockedSections()
  setupPasswordInput()

  // Initialize child attributes toggles
  initializeChildAttributesToggle()

  // Get all navigation links and sections
  const navLinks = document.querySelectorAll('.nav-link')
  const sections = document.querySelectorAll('.section, .endpoint-section')

  // Function to update active navigation link and sub-navigation
  function updateActiveNavLink() {
    let currentSection = ''

    // Find the current section in viewport
    sections.forEach((section) => {
      const rect = section.getBoundingClientRect()
      if (rect.top <= 120 && rect.bottom >= 120) {
        // Account for floating header
        currentSection = section.id
      }
    })

    // Update active state for navigation links
    navLinks.forEach((link) => {
      link.classList.remove('active')
      const href = link.getAttribute('href')

      if (href === `#${currentSection}`) {
        link.classList.add('active')
      }
    })

    // Handle sub-navigation visibility and highlighting
    Object.keys(SECTION_SUBNAV).forEach((parentSectionId) => {
      const subSectionIds = SECTION_SUBNAV[parentSectionId]
      const parentLink = document.querySelector(`a[href="#${parentSectionId}"]`)

      if (parentLink) {
        const parentLi = parentLink.parentElement
        const subNav = parentLi.querySelector('.sub-nav')

        if (subNav) {
          // Check if current section is the parent or any of its children
          const isParentActive = currentSection === parentSectionId
          const isChildActive = subSectionIds.includes(currentSection)

          if (isParentActive || isChildActive) {
            // Show sub-navigation
            subNav.style.display = 'block'

            // If we're in a child section, also highlight the parent
            if (isChildActive) {
              parentLink.classList.add('active')
            }
          } else {
            // Hide sub-navigation if not in this section group
            subNav.style.display = 'none'
          }
        }
      }
    })

    // Auto-scroll sidebar to keep active section at 1/3 from top
    scrollSidebarToActiveSection()
  }

  // Function to auto-scroll sidebar with throttling
  let sidebarScrollTimeout
  function scrollSidebarToActiveSection() {
    // Clear any existing timeout to throttle scroll events
    clearTimeout(sidebarScrollTimeout)

    sidebarScrollTimeout = setTimeout(() => {
      const sidebar = document.querySelector('.sidebar')
      const activeLink = document.querySelector('.nav-link.active')

      if (activeLink && sidebar) {
        const sidebarHeight = sidebar.clientHeight
        const targetPosition = sidebarHeight / 3 // 1/3 from top

        // Get the position of the active link relative to the sidebar
        const activeLinkRect = activeLink.getBoundingClientRect()
        const sidebarRect = sidebar.getBoundingClientRect()
        const activeLinkRelativeTop =
          activeLinkRect.top - sidebarRect.top + sidebar.scrollTop

        // Calculate the scroll position to place active link at 1/3 from top
        const newScrollTop = activeLinkRelativeTop - targetPosition

        // Only scroll if the active link is significantly out of the target area
        const currentScrollTop = sidebar.scrollTop
        const scrollDifference = Math.abs(newScrollTop - currentScrollTop)

        // Only auto-scroll if the difference is significant (more than 50px)
        if (scrollDifference > 50) {
          // Smooth scroll to the new position
          sidebar.scrollTo({
            top: Math.max(0, newScrollTop), // Don't scroll above 0
            behavior: 'smooth',
          })
        }
      }
    }, 100) // 100ms delay to throttle
  }

  // Smooth scroll to section when clicking navigation links
  navLinks.forEach((link) => {
    link.addEventListener('click', function (e) {
      e.preventDefault()
      const targetId = this.getAttribute('href').substring(1)
      const targetSection = document.getElementById(targetId)

      if (targetSection) {
        // Clear any pending sidebar scroll to prevent conflicts
        clearTimeout(sidebarScrollTimeout)

        const offsetTop = targetSection.offsetTop - 100 // Account for floating header
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth',
        })

        // Delay sidebar scroll until main scroll is likely complete
        setTimeout(() => {
          scrollSidebarToActiveSection()
        }, 500)
      }
    })
  })

  // Update active navigation on scroll
  let ticking = false
  function onScroll() {
    if (!ticking) {
      requestAnimationFrame(() => {
        updateActiveNavLink()
        ticking = false
      })
      ticking = true
    }
  }

  window.addEventListener('scroll', onScroll)

  // Initial call to set active navigation
  updateActiveNavLink()

  // Mobile menu toggle (for future mobile implementation)
  function createMobileMenuToggle() {
    const sidebar = document.querySelector('.sidebar')
    const content = document.querySelector('.content')

    // Create mobile menu button
    const menuButton = document.createElement('button')
    menuButton.className = 'mobile-menu-toggle'
    menuButton.innerHTML = '☰'
    menuButton.style.cssText = `
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1000;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 0.375rem;
            font-size: 1.25rem;
            cursor: pointer;
        `

    document.body.appendChild(menuButton)

    // Toggle sidebar on mobile
    menuButton.addEventListener('click', function () {
      sidebar.classList.toggle('open')
    })

    // Close sidebar when clicking outside on mobile
    content.addEventListener('click', function () {
      if (window.innerWidth <= 1024) {
        sidebar.classList.remove('open')
      }
    })

    // Show/hide mobile menu button based on screen size
    function checkScreenSize() {
      if (window.innerWidth <= 1024) {
        menuButton.style.display = 'block'
      } else {
        menuButton.style.display = 'none'
        sidebar.classList.remove('open')
      }
    }

    window.addEventListener('resize', checkScreenSize)
    checkScreenSize()
  }

  createMobileMenuToggle()

  // Copy code functionality
  function addCopyButtons() {
    const codeBlocks = document.querySelectorAll('.code-example pre')

    codeBlocks.forEach((block) => {
      const copyButton = document.createElement('button')
      copyButton.className = 'copy-button'
      copyButton.innerHTML = 'Copy'
      copyButton.style.cssText = `
                position: absolute;
                top: 0.75rem;
                right: 1rem;
                background: #374151;
                color: #d1d5db;
                border: 1px solid #4b5563;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                cursor: pointer;
                transition: all 0.2s ease;
            `

      copyButton.addEventListener('mouseenter', function () {
        this.style.background = '#4b5563'
        this.style.color = '#f9fafb'
      })

      copyButton.addEventListener('mouseleave', function () {
        this.style.background = '#374151'
        this.style.color = '#d1d5db'
      })

      copyButton.addEventListener('click', function () {
        const code = block.querySelector('code')
        const text = code.textContent

        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.innerHTML = 'Copied!'
            this.style.background = '#059669'
            this.style.borderColor = '#059669'

            setTimeout(() => {
              this.innerHTML = 'Copy'
              this.style.background = '#374151'
              this.style.borderColor = '#4b5563'
            }, 2000)
          })
          .catch((err) => {
            console.error('Failed to copy text: ', err)
          })
      })

      // Make the parent code example relative positioned
      const codeExample = block.closest('.code-example')
      codeExample.style.position = 'relative'
      codeExample.appendChild(copyButton)
    })
  }

  addCopyButtons()

  // Search functionality (basic implementation)
  function addSearchFunctionality() {
    const sidebar = document.querySelector('.sidebar')
    const searchContainer = document.createElement('div')
    searchContainer.style.cssText = `
            padding: 0 2rem 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1rem;
        `

    const searchInput = document.createElement('input')
    searchInput.type = 'text'
    searchInput.placeholder = 'Search documentation...'
    searchInput.style.cssText = `
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background-color: white;
        `

    searchContainer.appendChild(searchInput)
    const firstNavSection = sidebar.querySelector('.nav-section')
    sidebar.insertBefore(searchContainer, firstNavSection)

    // Simple search functionality
    searchInput.addEventListener('input', function () {
      const searchTerm = this.value.toLowerCase()
      const navSections = document.querySelectorAll('.nav-section')

      navSections.forEach((section) => {
        // Skip protected sections that are not unlocked
        if (
          section.classList.contains('protected-section') &&
          section.style.display === 'none'
        ) {
          return
        }

        const links = section.querySelectorAll('.nav-link:not(.sub-nav-link)')
        const subNavs = section.querySelectorAll('.sub-nav')
        let hasVisibleLinks = false

        // Check main navigation links
        links.forEach((link) => {
          const text = link.textContent.toLowerCase()
          if (text.includes(searchTerm)) {
            link.style.display = 'block'
            hasVisibleLinks = true
          } else {
            link.style.display = searchTerm ? 'none' : 'block'
          }
        })

        // Check sub-navigation links
        subNavs.forEach((subNav) => {
          const subLinks = subNav.querySelectorAll('.sub-nav-link')
          let hasVisibleSubLinks = false

          subLinks.forEach((link) => {
            const text = link.textContent.toLowerCase()
            if (text.includes(searchTerm)) {
              link.style.display = 'flex'
              hasVisibleSubLinks = true
              hasVisibleLinks = true
            } else {
              link.style.display = searchTerm ? 'none' : 'flex'
            }
          })

          // Show sub-nav if it has visible links or if no search term
          if (searchTerm) {
            subNav.style.display = hasVisibleSubLinks ? 'block' : 'none'
          } else {
            subNav.style.display = 'none' // Hide by default, will be shown by updateActiveNavLink
          }
        })

        // Hide section if no visible links
        if (searchTerm) {
          section.style.display = hasVisibleLinks ? 'block' : 'none'
        } else {
          // Restore original display state for protected sections
          if (
            section.classList.contains('protected-section') &&
            !section.classList.contains('unlocked')
          ) {
            section.style.display = 'none'
          } else {
            section.style.display = 'block'
          }
          // Trigger navigation update to show appropriate sub-navs
          updateActiveNavLink()
        }
      })
    })
  }

  addSearchFunctionality()
})
