import React, { useState } from 'react'
import { useRepos } from '../hooks/useRepos'
import DataTable, { type Column } from '../components/DataTable/DataTable'
import Pagination from '../components/Pagination/Pagination'
import './ActiveRepos.css'
import type { WorkRepo } from '../types'

const ActiveRepos: React.FC = () => {
  const [page, setPage] = useState(1)
  const limit = 50

  const { data, isLoading, error } = useRepos({ page, limit })

  // Define table columns
  const columns: Column<WorkRepo>[] = [
    {
      key: 'action',
      header: 'Actions',
      className: 'action-column',
      render: () => (
        <button className="btn-action btn-view">
          View
        </button>
      )
    },
    {
      key: 'id',
      header: 'ID',
      render: (value) => `#${value}`
    },
    {
      key: 'creator_email',
      header: 'Creator'
    },
    {
      key: 'repo_type',
      header: 'Type'
    },
    {
      key: 'customer_fault',
      header: 'Customer Fault',
      render: (value) => (
        <span className={`badge ${value ? 'yes' : 'no'}`}>
          {value ? 'Yes' : 'No'}
        </span>
      )
    },
    {
      key: 'units_affected',
      header: 'Units Affected'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading active repositions...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="error">
        <h2>Error Loading Active Repositions</h2>
        <p>Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  const repos = data?.data || []
  const total = data?.total || 0
  const totalPages = Math.ceil(total / limit)

  return (
    <div className="active-repos">
      <div className="header">
        <h1>Active Repositions</h1>
        <p>Manufacturing orders currently being processed for rework</p>
      </div>

      <DataTable
        data={repos}
        columns={columns}
        keyField="id"
        minWidth="600px"
        emptyMessage="There are currently no active repositions in the system."
      />

      <Pagination
        currentPage={page}
        totalPages={totalPages}
        onPageChange={setPage}
        total={total}
        limit={limit}
      />
    </div>
  )
}

export default ActiveRepos
