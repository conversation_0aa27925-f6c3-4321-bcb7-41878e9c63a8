import React, { useState } from 'react'
import { useRepos } from '../hooks/useRepos'
import './ActiveRepos.css'

const ActiveRepos: React.FC = () => {
  const [page, setPage] = useState(1)
  const limit = 50
  const offset = (page - 1) * limit

  const { data, isLoading, error } = useRepos({ page, limit })

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading active repositions...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="error">
        <h2>Error Loading Active Repositions</h2>
        <p>Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  const repos = data?.data || []
  const total = data?.total || 0
  const totalPages = Math.ceil(total / limit)

  return (
    <div className="active-repos">
      <div className="header">
        <h1>Active Repositions</h1>
        <p>Manufacturing orders currently being processed for rework</p>
      </div>

      {repos.length === 0 ? (
        <div className="empty-state">
          <h3>No Active Repositions</h3>
          <p>There are currently no active repositions in the system.</p>
        </div>
      ) : (
        <>
          <div className="repos-table-container">
            <table className="repos-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>MO ID</th>
                  <th>Creator</th>
                  <th>Type</th>
                  <th>Customer Fault</th>
                  <th>Units Affected</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {repos.map((repo) => (
                  <tr key={repo.id}>
                    <td>#{repo.id}</td>
                    <td>{repo.mo_id}</td>
                    <td>{repo.creator_email || 'N/A'}</td>
                    <td>{repo.repo_type || 'N/A'}</td>
                    <td>
                      <span className={`badge ${repo.customer_fault ? 'yes' : 'no'}`}>
                        {repo.customer_fault ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td>{repo.units_affected || 'N/A'}</td>
                    <td>{new Date(repo.created_at).toLocaleDateString()}</td>
                    <td>
                      <button className="btn-view">View</button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="pagination">
              <button
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
                className="btn-page"
              >
                Previous
              </button>
              
              <span className="page-info">
                Page {page} of {totalPages}
              </span>
              
              <button
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
                className="btn-page"
              >
                Next
              </button>
            </div>
          )}

          <div className="pagination-info">
            Showing {offset + 1}-{Math.min(offset + limit, total)} of {total} results
          </div>
        </>
      )}
    </div>
  )
}

export default ActiveRepos
