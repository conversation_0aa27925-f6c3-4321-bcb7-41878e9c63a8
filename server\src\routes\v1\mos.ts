import { Router } from 'express'
import * as moController from '../../controllers/moController'

const router = Router()

// GET /api/v1/mos/search - Search MOs
router.get('/search', moController.searchMos)

// GET /api/v1/mos/available - Get available MOs (those without work repos)
router.get('/available', moController.getAvailableMos)

// GET /api/v1/mos/:id/scans - Get scans for MO (put before /:id to avoid conflicts)
router.get('/:id/scans', moController.getScansByMoId)

// GET /api/v1/mos/:id - Get MO by ID
router.get('/:id', moController.getMoById)

export default router
