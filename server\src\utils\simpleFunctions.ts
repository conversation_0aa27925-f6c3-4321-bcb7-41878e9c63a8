type PrimitiveValue = string | number | boolean | null | undefined

export const valueStringOrNull = (value: PrimitiveValue): string | null => {
  if (value === undefined) {
    return null
  }
  if (value === null) {
    return null
  }
  if (typeof value === 'number' || typeof value === 'boolean') {
    return value.toString()
  }
  if (typeof value !== 'string') {
    return null
  }
  if (value.trim() === '') {
    return null
  }
  return value.trim()
}
