import { useQuery } from '@tanstack/react-query'
import { moService } from '../services/moService'
import type { RepoSearchParams } from '../types'

// Query keys
export const moKeys = {
  all: ['mos'] as const,
  search: (params: RepoSearchParams) => [...moKeys.all, 'search', params] as const,
  available: (limit: number, offset: number) => [...moKeys.all, 'available', limit, offset] as const,
  detail: (id: number) => [...moKeys.all, 'detail', id] as const,
  scans: (moId: number) => [...moKeys.all, 'scans', moId] as const,
}

// Hooks
export const useSearchMos = (params: RepoSearchParams) => {
  return useQuery({
    queryKey: moKeys.search(params),
    queryFn: () => moService.searchMos(params),
    enabled: !!(params.mo_order || params.customer || params.num),
  })
}

export const useAvailableMos = (limit = 50, offset = 0) => {
  return useQuery({
    queryKey: moKeys.available(limit, offset),
    queryFn: () => moService.getAvailableMos(limit, offset),
  })
}

export const useMo = (id: number) => {
  return useQuery({
    queryKey: moKeys.detail(id),
    queryFn: () => moService.getMoById(id),
    enabled: !!id,
  })
}

export const useMoScans = (moId: number) => {
  return useQuery({
    queryKey: moKeys.scans(moId),
    queryFn: () => moService.getScansByMoId(moId),
    enabled: !!moId,
  })
}
