/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-page {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 0.375rem 0.875rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-page:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-weight: 500;
}

.pagination-info {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .pagination-nav {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .pagination .page-info {
    text-align: center;
    order: -1;
  }

  .btn-page {
    padding: 0.5rem 0.875rem;
    font-size: 0.9rem;
  }

  .pagination-info {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .pagination {
    gap: 0.5rem;
  }

  .btn-page {
    padding: 0.375rem 0.75rem;
    font-size: 0.85rem;
  }
}
