/* MO Selection Styles */
.mo-selection {
  max-width: 1200px;
  margin: 0 auto;
}

.search-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.search-section h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.search-indicator {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border-left: 4px solid #1976d2;
}

.search-indicator span {
  font-weight: 500;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #333;
  font-weight: 600;
  margin-bottom: 0.4rem;
  font-size: 0.9rem;
}

.form-group input {
  padding: 0.6rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-search,
.btn-clear {
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-search {
  background: #667eea;
  color: white;
}

.btn-search:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-clear {
  background: #6c757d;
  color: white;
}

.btn-clear:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.results-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.results-section h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.mos-table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.mos-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
}

.mos-table th {
  background: #f8f9fa;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  font-size: 0.9rem;
}

.mos-table th:first-child {
  width: 100px;
  text-align: center;
}

.mos-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  color: #555;
  font-size: 0.9rem;
}

.mos-table td:first-child {
  text-align: center;
  padding: 0.5rem;
}

.mos-table tr:hover {
  background: #f8f9fa;
}

.btn-select {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  min-width: 60px;
  line-height: 1.2;
}

.btn-select:hover {
  background: #218838;
  transform: translateY(-1px);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-page {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 0.375rem 0.875rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-page:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-weight: 500;
}

.pagination-info {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.loading {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
  }

  .mo-selection {
    margin: 0 1rem;
  }
}

@media (max-width: 768px) {
  .mo-selection {
    margin: 0 0.5rem;
  }

  .search-section,
  .results-section {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
  }

  .search-section h2 {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .results-section h3 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .form-group input {
    padding: 0.75rem;
    font-size: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-search,
  .btn-clear {
    width: 100%;
    padding: 0.625rem 1rem;
    font-size: 1rem;
  }

  .mos-table-container {
    margin-bottom: 1rem;
  }

  .mos-table {
    min-width: 500px;
  }

  .mos-table th,
  .mos-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }

  .mos-table th:first-child,
  .mos-table td:first-child {
    width: 80px;
    padding: 0.25rem;
  }

  .mos-table th:last-child,
  .mos-table td:last-child {
    padding-right: 0.5rem;
  }

  .btn-select {
    padding: 0.375rem 0.625rem;
    font-size: 0.85rem;
  }

  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .pagination-nav {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .pagination .page-info {
    text-align: center;
    order: -1;
  }

  .btn-page {
    padding: 0.5rem 0.875rem;
    font-size: 0.9rem;
  }

  .pagination-info {
    font-size: 0.85rem;
  }

  .empty-state {
    padding: 1.5rem;
  }

  .loading {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .mo-selection {
    margin: 0 0.25rem;
  }

  .search-section,
  .results-section {
    padding: 0.75rem;
    border-radius: 6px;
  }

  .search-section h2 {
    font-size: 1.1rem;
  }

  .results-section h3 {
    font-size: 1rem;
  }

  .form-group input {
    padding: 0.875rem;
  }

  .mos-table {
    min-width: 450px;
  }

  .mos-table th,
  .mos-table td {
    padding: 0.4rem 0.2rem;
    font-size: 0.8rem;
  }

  .mos-table th:first-child,
  .mos-table td:first-child {
    width: 70px;
    padding: 0.2rem;
  }

  .btn-select {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .pagination {
    gap: 0.5rem;
  }

  .btn-page {
    padding: 0.375rem 0.75rem;
    font-size: 0.85rem;
  }
}
