/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-code: #1a1b26;
  --bg-code-header: #1f2335;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;
  --text-code: #a9b1d6;
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --accent-active: #1d4ed8;
  --success: #059669;
  --error: #dc2626;
  --floating-header-bg: rgba(255, 255, 255, 0.95);
}

[data-theme='dark'] {
  /* Dark theme colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-code: #0f172a;
  --bg-code-header: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #e2e8f0;
  --text-muted: #94a3b8;
  --text-code: #a9b1d6;
  --border-primary: #334155;
  --border-secondary: #475569;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --accent-active: #1d4ed8;
  --success: #10b981;
  --error: #ef4444;
  --floating-header-bg: rgba(15, 23, 42, 0.95);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: var(--text-secondary);
  background-color: var(--bg-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Container and layout */
.container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar navigation */
.sidebar {
  width: 280px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  padding: 2rem 0;
  z-index: 100;
  scroll-behavior: smooth;
}

.sidebar-header {
  padding: 0 2rem 2rem 2rem;
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: 2rem;
}

.sidebar-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.version {
  font-size: 0.875rem;
  color: var(--text-muted);
  background-color: var(--border-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  display: inline-block;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 2rem;
  margin-bottom: 1rem;
}

.nav-section ul {
  list-style: none;
}

.nav-section li {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: block;
  padding: 0.5rem 2rem;
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
}

.nav-link.active {
  color: var(--accent-primary);
  background-color: var(--bg-tertiary);
  border-left-color: var(--accent-primary);
  font-weight: 500;
}

/* Sub-navigation styles */
.sub-nav {
  list-style: none;
  margin: 0.5rem 0 0 0;
  padding: 0;
  background-color: var(--bg-secondary);
  border-left: 2px solid var(--border-light);
  margin-left: 1rem;
}

.sub-nav li {
  margin-bottom: 0;
}

.sub-nav .nav-link {
  padding: 0.5rem 1rem 0.5rem 2rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  border-left: 2px solid transparent;
  margin-left: 0;
}

.sub-nav .nav-link:hover {
  color: var(--accent-primary);
  background-color: var(--bg-tertiary);
  border-left-color: var(--accent-primary);
}

.method-badge {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 35px;
  text-align: center;
}

.method-badge.get {
  background-color: #dcfce7;
  color: #166534;
}

.method-badge.post {
  background-color: #dbeafe;
  color: #1e40af;
}

.method-badge.put {
  background-color: #fef3c7;
  color: #92400e;
}

.method-badge.delete {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Main content */
.content {
  flex: 1;
  margin-left: 280px;
  padding-top: 80px; /* Account for floating header */
}

.section {
  border-bottom: 1px solid #f3f4f6;
}

.endpoint-section {
  border-bottom: 1px solid #f9fafb;
}

.section-content {
  display: flex;
  min-height: auto;
}

.text-column {
  flex: 1;
  padding: 4rem 3rem;
  background-color: var(--bg-primary);
  max-width: 50%;
}

.code-column {
  flex: 1;
  padding: 4rem 3rem;
  background-color: var(--bg-code);
  max-width: 50%;
}

/* Typography */
h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 2rem 0 1rem 0;
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 1.5rem 0 0.75rem 0;
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

ul {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

/* Code styling */
.inline-code {
  background-color: #f3f4f6;
  color: #e11d48;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.code-example {
  margin-bottom: 2rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #24283b;
}

.code-header {
  background-color: #1f2335;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #414868;
  font-size: 0.875rem;
  color: #a9b1d6;
  font-weight: 500;
}

.code-example pre {
  margin: 0;
  padding: 1.5rem;
  background-color: #24283b;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Endpoint styling */
.endpoint-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.method {
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.method.get {
  background-color: #dcfce7;
  color: #166534;
}

.method.post {
  background-color: #dbeafe;
  color: #1e40af;
}

.method.put {
  background-color: #fef3c7;
  color: #92400e;
}

.method.delete {
  background-color: #fee2e2;
  color: #dc2626;
}

.endpoint-path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

/* Responsive design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
  }

  .floating-header {
    left: 0;
  }

  .header-content {
    padding: 0 1.5rem;
  }

  .header-login {
    align-items: center;
  }

  .section-content {
    flex-direction: column;
  }

  .text-column,
  .code-column {
    max-width: 100%;
    padding: 2rem 1.5rem;
  }

  .code-column {
    background-color: #1a1b26;
  }
}

@media (max-width: 768px) {
  .text-column,
  .code-column {
    padding: 1.5rem 1rem;
  }

  h1 {
    font-size: 2rem;
  }

  .endpoint-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .header-title {
    text-align: center;
  }

  .header-login {
    align-items: center;
    width: 100%;
  }

  .login-form {
    width: 100%;
    max-width: 300px;
  }

  .password-input {
    min-width: 0;
    flex: 1;
  }

  .floating-header {
    padding: 0.75rem 0;
  }

  .content {
    padding-top: 120px; /* More space for mobile header */
  }
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Floating header styles */
.floating-header {
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  background: var(--floating-header-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-primary);
  z-index: 50;
  padding: 1rem 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  padding: 0 3rem;
}

.header-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-selector {
  display: flex;
  align-items: center;
}

.theme-toggle {
  background: none;
  border: 1px solid var(--border-secondary);
  border-radius: 0.375rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-primary);
}

.theme-icon {
  font-size: 1.125rem;
  transition: transform 0.2s ease;
}

.header-login {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.login-form {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.password-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-secondary);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  transition: border-color 0.2s ease;
  min-width: 200px;
}

.password-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.unlock-button {
  padding: 0.5rem 1rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.unlock-button:hover {
  background-color: var(--accent-hover);
}

.unlock-button:active {
  background-color: var(--accent-active);
}

.login-status {
  font-size: 0.75rem;
  min-height: 1rem;
  text-align: right;
}

.login-status.success {
  color: var(--success);
}

.login-status.error {
  color: var(--error);
}

/* Protected content styles */
.protected-section {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.protected-section.unlocked {
  opacity: 1;
}

.protected-content {
  position: relative;
}

.protected-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 40%,
    rgba(0, 0, 0, 0.05) 50%,
    transparent 60%
  );
  pointer-events: none;
  z-index: 1;
}

.protected-content.unlocked::before {
  display: none;
}

/* Property block styling */
.property-block {
  border-top: 1px solid var(--border-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0.5rem 0;
  margin: 0;
}

.property-block + .property-block {
  border-top: none;
}

.property-block:first-of-type {
  margin-top: 0.75rem;
}

.property-block:last-of-type {
  margin-bottom: 0.75rem;
}

.property-name {
  font-family: 'Source Code Pro', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: var(--text-primary);
}

.property-type {
  color: #9ca3af;
  font-size: 0.75rem;
  margin-left: 0.5rem;
  font-weight: 400;
}

.property-required {
  color: #ef4444;
  font-size: 0.75rem;
  margin-left: 0.5rem;
  font-weight: 500;
  background-color: #fef2f2;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  border: 1px solid #fecaca;
}

.property-description {
  margin-top: 0.25rem;
  margin-bottom: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.875rem;
}

/* Remove margin-bottom from p tags in property blocks */
.property-block p {
  margin-bottom: 0;
}

/* Child attributes toggle sections */
.child-attributes-section {
  border-top: 1px solid var(--border-primary);
  border-bottom: 1px solid var(--border-primary);
  margin: 0;
}

.child-attributes-section + .child-attributes-section {
  border-top: none;
}

.child-attributes-section:first-of-type {
  margin-top: 1rem;
}

.child-attributes-section:last-of-type {
  margin-bottom: 1rem;
}

.child-attributes-header {
  padding: 0.75rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.child-attributes-toggle {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.child-attributes-toggle:hover {
  background-color: var(--bg-tertiary);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.child-attributes-toggle.expanded {
  background-color: var(--bg-secondary);
}

.child-attributes-toggle.expanded:hover {
  background-color: var(--bg-tertiary);
}

.toggle-icon {
  font-size: 0.875rem;
  font-weight: bold;
  transition: all 0.2s ease;
  min-width: 12px;
  text-align: center;
}

.child-attributes-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-top: 0;
}

.child-attributes-content.expanded {
  max-height: 3000px;
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  background-color: var(--bg-secondary);
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.child-attributes-expanded-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  border-radius: 20px 20px 0 0;
  background-color: var(--bg-secondary);
  cursor: pointer;
  width: 100%;
}

.child-attributes-expanded-content {
  padding: 1rem;
}

/* Add extra bottom padding for nested child attributes sections */
.child-attributes-expanded-content .child-attributes-section {
  margin-bottom: 1rem;
}

.child-attributes-expanded-content .child-attributes-content.expanded {
  margin-bottom: 1rem;
}

.child-attributes-toggle.inside-box {
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  width: 100%;
}

.child-attributes-toggle.inside-box:hover {
  color: var(--text-primary);
  background-color: transparent;
  transform: none;
  box-shadow: none;
}

.child-attributes-toggle.inside-box .toggle-icon {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-muted);
}

.child-attributes-content .property-block:first-of-type {
  margin-top: 0;
}

.child-attributes-content .property-block:last-of-type {
  margin-bottom: 0;
}

/* Endpoint list styling */
.endpoint-list {
  padding: 1rem;
}

.endpoint-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: var(--bg-tertiary);
}

.endpoint-item:last-child {
  margin-bottom: 0;
}

.endpoint-url {
  font-family: 'Source Code Pro', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
