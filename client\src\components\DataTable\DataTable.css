/* Data Table Styles */
.data-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.data-table-container.compact {
  margin-bottom: 1.5rem;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  font-size: 0.9rem;
}

.data-table th.action-column {
  width: 100px;
  text-align: center;
}

.data-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  color: #555;
  font-size: 0.9rem;
}

.data-table td.action-column {
  text-align: center;
  padding: 0.5rem;
}

.data-table tr:hover {
  background: #f8f9fa;
}

/* Badge Styles */
.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.yes {
  background: #dc3545;
  color: white;
}

.badge.no {
  background: #28a745;
  color: white;
}

/* Button Styles */
.btn-action {
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  min-width: 60px;
  line-height: 1.2;
}

.btn-action:hover {
  transform: translateY(-1px);
}

.btn-select {
  background: #28a745;
  color: white;
}

.btn-select:hover {
  background: #218838;
}

.btn-view {
  background: #667eea;
  color: white;
}

.btn-view:hover {
  background: #5a6fd8;
}

.btn-prepare {
  background: #ffc107;
  color: #212529;
}

.btn-prepare:hover {
  background: #e0a800;
}

/* Loading and Empty States */
.data-table-loading {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.data-table-empty {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .data-table-container {
    margin: 0 1rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .data-table-container {
    margin: 0 0.5rem 1rem;
    border-radius: 8px;
    overflow-x: auto;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }

  .data-table th.action-column,
  .data-table td.action-column {
    width: 80px;
    padding: 0.25rem;
  }

  .data-table th:last-child,
  .data-table td:last-child {
    padding-right: 0.5rem;
  }

  .btn-action {
    padding: 0.375rem 0.625rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .data-table-container {
    margin: 0 0.25rem 1rem;
    border-radius: 6px;
  }

  .data-table th,
  .data-table td {
    padding: 0.4rem 0.2rem;
    font-size: 0.8rem;
  }

  .data-table th.action-column,
  .data-table td.action-column {
    width: 70px;
    padding: 0.2rem;
  }

  .btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}
