import React from 'react'

export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
  title?: string
}

export const Form: React.FC<FormProps> = ({
  children,
  title,
  className = '',
  ...props
}) => {
  const classes = `card ${className}`
  
  return (
    <form className={classes} {...props}>
      {title && (
        <h2 className="text-xl font-semibold text-gray-900 mb-6">{title}</h2>
      )}
      {children}
    </form>
  )
}

export interface FormRowProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export const FormRow: React.FC<FormRowProps> = ({
  children,
  columns = 2,
  className = ''
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }
  
  const classes = `grid ${gridClasses[columns]} gap-4 ${className}`
  
  return (
    <div className={classes}>
      {children}
    </div>
  )
}

export interface FormActionsProps {
  children: React.ReactNode
  className?: string
  align?: 'left' | 'center' | 'right'
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  className = '',
  align = 'right'
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  }
  
  const classes = `flex flex-col sm:flex-row gap-3 ${alignClasses[align]} pt-6 border-t border-gray-200 ${className}`
  
  return (
    <div className={classes}>
      {children}
    </div>
  )
}
