import { db } from '../db/database'
import { MoNumber, MoScan } from '../db/models'

export class MoService {
  async getAllMos(): Promise<MoNumber[]> {
    return await db.selectFrom('mo_numbers').selectAll().execute()
  }

  async getMoById(moId: number): Promise<MoNumber | undefined> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where('mo_id', '=', moId)
      .executeTakeFirst()
  }

  async searchMos(searchParams: {
    mo_order?: string
    customer?: string
    num?: string
    limit?: number
    offset?: number
  }): Promise<{
    data: MoNumber[]
    total: number
    page: number
    limit: number
    totalPages: number
    hasMore: boolean
  }> {
    let query = db.selectFrom('mo_numbers').selectAll()
    let countQuery = db
      .selectFrom('mo_numbers')
      .select(db.fn.count('mo_id').as('count'))

    // Add search conditions to both queries
    if (searchParams.mo_order) {
      query = query.where('mo_order', 'like', `%${searchParams.mo_order}%`)
      countQuery = countQuery.where(
        'mo_order',
        'like',
        `%${searchParams.mo_order}%`
      )
    }

    if (searchParams.customer) {
      const customerCondition = (eb: any) =>
        eb.or([
          eb('customer', 'like', `%${searchParams.customer}%`),
          eb('customers', 'like', `%${searchParams.customer}%`),
        ])
      query = query.where(customerCondition)
      countQuery = countQuery.where(customerCondition)
    }

    if (searchParams.num) {
      query = query.where('num', 'like', `%${searchParams.num}%`)
      countQuery = countQuery.where('num', 'like', `%${searchParams.num}%`)
    }

    const limit = Math.min(searchParams.limit || 100, 100) // Max 100 results
    const offset = searchParams.offset || 0

    // Add ordering and pagination
    query = query.orderBy('created_at', 'desc').limit(limit).offset(offset)

    const [data, countResult] = await Promise.all([
      query.execute(),
      countQuery.executeTakeFirst(),
    ])

    const total = Number(countResult?.count || 0)
    const hasMore = offset + limit < total
    const page = Math.floor(offset / limit) + 1
    const totalPages = Math.ceil(total / limit)

    return { data, total, page, limit, totalPages, hasMore }
  }

  async getMosByOrder(moOrder: string): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where('mo_order', '=', moOrder)
      .execute()
  }

  async getMosByCustomer(customer: string): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where((eb) =>
        eb.or([
          eb('customer', 'like', `%${customer}%`),
          eb('customers', 'like', `%${customer}%`),
        ])
      )
      .execute()
  }

  async getMosByNum(num: string): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where('num', 'like', `%${num}%`)
      .execute()
  }

  async getRecentMos(limit: number = 20): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .orderBy('created_at', 'desc')
      .limit(limit)
      .execute()
  }

  async getMosByStatus(status: string): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where('mo_status', '=', status)
      .execute()
  }

  async getActiveMos(): Promise<MoNumber[]> {
    return await db
      .selectFrom('mo_numbers')
      .selectAll()
      .where((eb) =>
        eb.and([
          eb('mo_status', '!=', 'COMPLETE'),
          eb('mo_status', '!=', 'CANCELLED'),
          eb('mo_status', '!=', 'CLOSED'),
        ])
      )
      .orderBy('created_at', 'desc')
      .execute()
  }

  // Get MOs that don't have associated work repos yet
  async getAvailableMos(
    limit: number = 100,
    offset: number = 0
  ): Promise<{
    data: MoNumber[]
    total: number
    page: number
    limit: number
    totalPages: number
    hasMore: boolean
  }> {
    const actualLimit = Math.min(limit, 100) // Max 100 results

    const countQuery = db
      .selectFrom('mo_numbers')
      .leftJoin('work_repos', 'mo_numbers.mo_id', 'work_repos.mo_id')
      .select(db.fn.count('mo_numbers.mo_id').as('count'))
      .where('work_repos.mo_id', 'is', null)
      .where((eb) =>
        eb.and([
          eb('mo_numbers.mo_status', '!=', 'COMPLETE'),
          eb('mo_numbers.mo_status', '!=', 'CANCELLED'),
          eb('mo_numbers.mo_status', '!=', 'CLOSED'),
        ])
      )

    const dataQuery = db
      .selectFrom('mo_numbers')
      .leftJoin('work_repos', 'mo_numbers.mo_id', 'work_repos.mo_id')
      .selectAll('mo_numbers')
      .where('work_repos.mo_id', 'is', null)
      .where((eb) =>
        eb.and([
          eb('mo_numbers.mo_status', '!=', 'COMPLETE'),
          eb('mo_numbers.mo_status', '!=', 'CANCELLED'),
          eb('mo_numbers.mo_status', '!=', 'CLOSED'),
        ])
      )
      .orderBy('mo_numbers.created_at', 'desc')
      .limit(actualLimit)
      .offset(offset)

    const [data, countResult] = await Promise.all([
      dataQuery.execute(),
      countQuery.executeTakeFirst(),
    ])

    const total = Number(countResult?.count || 0)
    const hasMore = offset + actualLimit < total
    const page = Math.floor(offset / actualLimit) + 1
    const totalPages = Math.ceil(total / actualLimit)

    return { data, total, page, limit: actualLimit, totalPages, hasMore }
  }

  // MO Scans methods
  async getScansByMoId(moId: number): Promise<MoScan[]> {
    return await db
      .selectFrom('mo_scans')
      .selectAll()
      .where('mo_id', '=', moId)
      .where('removed_at', 'is', null)
      .orderBy('created_at', 'desc')
      .execute()
  }

  async getRecentScansByMoId(
    moId: number,
    limit: number = 10
  ): Promise<MoScan[]> {
    return await db
      .selectFrom('mo_scans')
      .selectAll()
      .where('mo_id', '=', moId)
      .where('removed_at', 'is', null)
      .orderBy('created_at', 'desc')
      .limit(limit)
      .execute()
  }
}

// Export a singleton instance
export const moService = new MoService()
