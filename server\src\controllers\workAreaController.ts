import { Request, Response } from 'express'
import { workAreaService } from '../services/workAreaService'

// Get all work areas
export const getAllWorkAreas = async (req: Request, res: Response) => {
  try {
    const workAreas = await workAreaService.getAllWorkAreas()
    res.json({
      success: true,
      data: workAreas,
      total: workAreas.length,
      page: 1,
      limit: workAreas.length,
      totalPages: 1,
      hasMore: false,
    })
  } catch (error) {
    console.error('Error getting work areas:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get work areas',
    })
  }
}

// Get active work areas
export const getActiveWorkAreas = async (req: Request, res: Response) => {
  try {
    const workAreas = await workAreaService.getActiveWorkAreas()
    res.json({
      success: true,
      data: workAreas,
      total: workAreas.length,
      page: 1,
      limit: workAreas.length,
      totalPages: 1,
      hasMore: false,
    })
  } catch (error) {
    console.error('Error getting active work areas:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get active work areas',
    })
  }
}
