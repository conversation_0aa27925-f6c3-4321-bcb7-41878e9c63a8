import React from 'react'
import { Link, useLocation } from 'react-router-dom'

export interface NavLinkProps {
  to: string
  children: React.ReactNode
  className?: string
}

export const NavLink: React.FC<NavLinkProps> = ({
  to,
  children,
  className = ''
}) => {
  const location = useLocation()
  const isActive = location.pathname === to
  
  const baseClasses = 'px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200'
  const activeClasses = isActive 
    ? 'bg-primary-100 text-primary-700' 
    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
  
  const classes = `${baseClasses} ${activeClasses} ${className}`
  
  return (
    <Link to={to} className={classes}>
      {children}
    </Link>
  )
}
