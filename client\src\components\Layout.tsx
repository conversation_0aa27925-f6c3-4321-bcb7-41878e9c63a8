import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import './Layout.css'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation()

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  return (
    <div className="app">
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="nav-brand">
            Reposition Tracker
          </Link>
          <ul className="nav-links">
            <li>
              <Link 
                to="/" 
                className={isActive('/') ? 'active' : ''}
              >
                Home
              </Link>
            </li>
            <li>
              <Link 
                to="/create-repo" 
                className={isActive('/create-repo') ? 'active' : ''}
              >
                Create Reposition
              </Link>
            </li>
            <li>
              <Link 
                to="/active-repos" 
                className={isActive('/active-repos') ? 'active' : ''}
              >
                Active Repositions
              </Link>
            </li>
            <li>
              <Link 
                to="/prepare-repos" 
                className={isActive('/prepare-repos') ? 'active' : ''}
              >
                Prepare Repositions
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      <main className="main-content">
        <div className="container">
          {children}
        </div>
      </main>
    </div>
  )
}

export default Layout
