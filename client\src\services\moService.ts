import { apiClient } from '../lib/api'
import type { MoNumber, MoScan, ApiResponse, RepoSearchParams } from '../types'

export const moService = {
  // Search MOs
  async searchMos(params: RepoSearchParams): Promise<ApiResponse<MoNumber[]>> {
    // This endpoint needs to be created on the server
    try {
      console.log('searching mos with params:', params)
      const response = await apiClient.get('/v1/mos/search', { params })
      console.log('search response:', response.data)
      return response.data
    } catch (error) {
      console.error('Error searching MOs:', error)
      throw error
    }
  },

  // Get available MOs (those without work repos)
  async getAvailableMos(
    limit = 50,
    offset = 0
  ): Promise<ApiResponse<MoNumber[]>> {
    // This endpoint needs to be created on the server
    const response = await apiClient.get('/v1/mos/available', {
      params: { limit, offset },
    })
    return response.data
  },

  // Get MO by ID
  async getMoById(id: number): Promise<MoNumber> {
    // This endpoint needs to be created on the server
    const response = await apiClient.get(`/v1/mos/${id}`)
    return response.data
  },

  // Get scans by MO ID
  async getScansByMoId(moId: number): Promise<MoScan[]> {
    // This endpoint needs to be created on the server
    const response = await apiClient.get(`/v1/mos/${moId}/scans`)
    return response.data
  },
}
