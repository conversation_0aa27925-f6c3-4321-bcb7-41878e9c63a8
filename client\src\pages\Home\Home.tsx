import React from 'react'
import { Link } from 'react-router-dom'
import { useHomeStats } from '@hooks/useRepos'
import { Button } from '@components/Button/Button'

const Home: React.FC = () => {
  const { data: stats, isLoading, error } = useHomeStats()

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
        <p className="text-gray-600">Loading repository statistics...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Home Page</h2>
        <p className="text-gray-600">Unable to load repository statistics: {error.message}</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Reposition Tracking System</h1>
        <p className="text-lg text-gray-600">Manufacturing Order Rework Management Dashboard</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card card-hover text-center">
          <div className="text-3xl font-bold text-primary-600 mb-2">{stats?.totalRepos || 0}</div>
          <div className="text-sm font-medium text-gray-600">Total Repositions</div>
        </div>
        <div className="card card-hover text-center">
          <div className="text-3xl font-bold text-success-600 mb-2">{stats?.activeRepos || 0}</div>
          <div className="text-sm font-medium text-gray-600">Active Repositions</div>
        </div>
        <div className="card card-hover text-center">
          <div className="text-3xl font-bold text-blue-600 mb-2">{stats?.completedRepos || 0}</div>
          <div className="text-sm font-medium text-gray-600">Completed Repositions</div>
        </div>
        <div className="card card-hover text-center">
          <div className="text-3xl font-bold text-orange-600 mb-2">{stats?.pendingRepos || 0}</div>
          <div className="text-sm font-medium text-gray-600">Pending Repositions</div>
        </div>
      </div>

      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link to="/create-repo">
            <Button variant="primary" className="w-full">
              Create New Reposition
            </Button>
          </Link>
          <Link to="/active-repos">
            <Button variant="secondary" className="w-full">
              View Active Repositions
            </Button>
          </Link>
          <Link to="/prepare-repos">
            <Button variant="secondary" className="w-full">
              Prepare Repositions
            </Button>
          </Link>
        </div>
      </div>

      {stats?.recentRepos && stats.recentRepos.length > 0 && (
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Repositions</h2>
          <div className="space-y-3">
            {stats.recentRepos.map((repo) => (
              <div key={repo.id} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="font-semibold text-gray-900 mb-1">Reposition #{repo.id}</div>
                <div className="text-sm text-gray-600">
                  {repo.creator_email && `Creator: ${repo.creator_email}`}
                  {repo.creator_email && repo.repo_type && ' | '}
                  {repo.repo_type && `Type: ${repo.repo_type}`}
                  {(repo.creator_email || repo.repo_type) && ' | '}
                  Created: {new Date(repo.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">API Endpoints</h3>
        <div className="space-y-2">
          <a
            href="/api/v1/repos"
            target="_blank"
            rel="noopener noreferrer"
            className="block text-primary-600 hover:text-primary-700 hover:underline"
          >
            All Repositions
          </a>
          <a
            href="/api/v1/repos/statuses"
            target="_blank"
            rel="noopener noreferrer"
            className="block text-primary-600 hover:text-primary-700 hover:underline"
          >
            Status Definitions
          </a>
          <a
            href="/api/v1"
            target="_blank"
            rel="noopener noreferrer"
            className="block text-primary-600 hover:text-primary-700 hover:underline"
          >
            API v1 Info
          </a>
        </div>
      </div>
    </div>
  )
}

export default Home
