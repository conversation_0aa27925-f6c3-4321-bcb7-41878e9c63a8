import { sql, type <PERSON>ysely } from 'kysely'

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .createTable('customers')
    .addColumn('id', 'serial', (col) => col.primaryKey())
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('name', 'text', (col) => col.notNull())
    .addColumn('poly_customer_codes', 'jsonb', (col) => col.notNull())
    .execute()
}

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function down(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema.dropTable('customers').execute()
}
