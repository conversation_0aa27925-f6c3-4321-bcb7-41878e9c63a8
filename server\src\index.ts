import 'dotenv/config'
import express, { Express, Request, Response } from 'express'
import { apiRouter } from './routes/api'
import path from 'path'
import cors from 'cors'

export const server = () => {
  const app: Express = express()
  const port = process.env.PORT || 3000

  // Enable CORS with environment-based configuration
  app.use(
    cors({
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true)

        // Get allowed origins from environment variable
        const corsOrigins = process.env.CORS_ORIGINS

        if (corsOrigins) {
          // Production or explicit CORS configuration
          const allowedOrigins = corsOrigins.split(',').map(origin => origin.trim())
          if (allowedOrigins.includes(origin)) {
            return callback(null, true)
          }
          return callback(new Error('Not allowed by CORS'))
        } else {
          // Development mode - allow any localhost port when CORS_ORIGINS is not set
          if (origin.startsWith('http://localhost:') || origin.startsWith('http://127.0.0.1:')) {
            return callback(null, true)
          }
          return callback(new Error('Not allowed by CORS'))
        }
      },
      credentials: true,
    })
  )

  app.use('/api', apiRouter)

  // Serve static files from React app
  app.use(express.static(path.join(__dirname, '../../client/dist')))

  // Serve React app for all non-API routes
  app.get('*path', (_req: Request, res: Response) => {
    res.sendFile(path.join(__dirname, '../../client/dist/index.html'))
  })

  app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`)
  })
}
server()
