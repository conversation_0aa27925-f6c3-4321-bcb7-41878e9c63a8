import { Router } from 'express'
import * as materialController from '../../controllers/materialController'

const router = Router()

// GET /api/v1/materials/search - Search for materials
router.get('/search', materialController.searchMaterials)

// GET /api/v1/materials/mo-materials - Get materials for a specific MO
router.get('/mo-materials', materialController.getMoMaterials)

// GET /api/v1/materials/:partNumber - Get material by part number
router.get('/:partNumber', materialController.getMaterialByPartNumber)

export default router
