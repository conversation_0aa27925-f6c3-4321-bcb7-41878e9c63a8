import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { repoService } from '../services/repoService'
import { moService } from '../services/moService'
import { workAreaService } from '../services/workAreaService'

// Helper function to generate the navigation HTML
function generateNavigation(activePage: string): string {
  return `
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">Reposition Tracker</a>
            <ul class="nav-links">
                <li><a href="/" ${activePage === 'home' ? 'class="active"' : ''}>Home</a></li>
                <li><a href="/create-repo" ${activePage === 'create' ? 'class="active"' : ''}>Create Reposition</a></li>
                <li><a href="/active-repos" ${activePage === 'active' ? 'class="active"' : ''}>Active Repositions</a></li>
                <li><a href="/prepare-repos" ${activePage === 'prepare' ? 'class="active"' : ''}>Prepare Repositions</a></li>
            </ul>
        </div>
    </nav>
  `
}

// Helper function to generate common CSS
function generateCommonCSS(): string {
  return `
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .nav-brand {
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            text-decoration: none;
            padding: 15px 0;
        }
        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .nav-links li {
            margin: 0;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 20px 20px;
            display: block;
            transition: background-color 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
            border-bottom-color: rgba(255,255,255,0.5);
        }
        .nav-links a.active {
            background-color: rgba(255,255,255,0.2);
            border-bottom-color: white;
        }
        .main-content {
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .repo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .repo-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .repo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .repo-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .repo-card p {
            margin: 5px 0;
            color: #666;
            font-size: 0.9em;
        }
        .repo-card .repo-id {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-prepare {
            background: #cce5ff;
            color: #004085;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .search-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }
        .mo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .mo-table th,
        .mo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .mo-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .mo-table tr:hover {
            background: #f8f9fa;
        }
        .mo-table .select-btn {
            background: #28a745;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }
        .mo-table .select-btn:hover {
            background: #218838;
        }
        .mo-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .mo-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .mo-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .mo-detail-item {
            display: flex;
            flex-direction: column;
        }
        .mo-detail-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
            margin-bottom: 2px;
        }
        .mo-detail-value {
            color: #333;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }
        .pagination-info {
            margin: 0 20px;
            color: #666;
            font-size: 0.9em;
        }
        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .pagination-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }
        .pagination-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .pagination-btn.disabled {
            background: #f8f9fa;
            color: #ccc;
            cursor: not-allowed;
            border-color: #eee;
        }
        .pagination-btn.disabled:hover {
            background: #f8f9fa;
            border-color: #eee;
        }
        .radio-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .radio-item input[type="radio"] {
            width: auto;
            margin: 0;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }
        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        .multi-select-container {
            position: relative;
        }
        .multi-select-input {
            cursor: pointer;
            background: white;
        }
        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .multi-select-dropdown.show {
            display: block;
        }
        .multi-select-option {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .multi-select-option:hover {
            background: #f8f9fa;
        }
        .multi-select-option input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        .selected-areas {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .selected-area-tag {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .selected-area-tag .remove {
            cursor: pointer;
            color: #f44336;
            font-weight: bold;
        }
        .material-selection {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
        }
        .material-search-container {
            position: relative;
            margin-bottom: 15px;
        }
        .material-search-input {
            cursor: pointer;
            background: white;
        }
        .material-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .material-dropdown.show {
            display: block;
        }
        .material-option {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .material-option:hover {
            background: #f8f9fa;
        }
        .material-option .part-number {
            font-weight: bold;
            color: #333;
        }
        .material-option .unit-measure {
            color: #666;
            font-size: 0.9em;
        }
        .material-option .usage-count {
            color: #999;
            font-size: 0.8em;
        }
        .selected-materials {
            margin-top: 15px;
        }
        .selected-material-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .selected-material-item .material-info {
            flex: 1;
        }
        .selected-material-item .part-number {
            font-weight: bold;
            color: #333;
        }
        .selected-material-item .unit-measure {
            color: #666;
            font-size: 0.9em;
        }
        .selected-material-item .quantity-input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .selected-material-item .remove-material {
            cursor: pointer;
            color: #f44336;
            font-weight: bold;
            padding: 5px 10px;
            border: 1px solid #f44336;
            border-radius: 4px;
            background: white;
        }
        .selected-material-item .remove-material:hover {
            background: #f44336;
            color: white;
        }
        .material-entry-row {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .material-entry-row .form-group {
            margin: 0;
            flex: 1;
        }
        .material-entry-row .form-group.units-group {
            flex: 0 0 100px;
        }
        .material-entry-row .form-group.quantity-group {
            flex: 0 0 120px;
        }
        .material-entry-row .form-group.action-group {
            flex: 0 0 auto;
        }
        .material-entry-row input {
            margin: 0;
        }
        .material-entry-row label {
            font-size: 0.9em;
            margin-bottom: 5px;
            display: block;
        }
        .add-material-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
        }
        .add-material-btn:hover {
            background: #45a049;
        }
        .remove-material-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }
        .remove-material-btn:hover {
            background: #da190b;
        }
        .material-search-suggestion {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 150px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .material-search-suggestion.show {
            display: block;
        }
        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        .suggestion-item:hover {
            background: #f8f9fa;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .suggestion-part-number {
            font-weight: bold;
            color: #333;
        }
        .suggestion-unit {
            color: #666;
            font-size: 0.8em;
        }
        .previous-repos {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .previous-repos h3 {
            margin: 0 0 15px 0;
            color: #e65100;
        }
        .previous-repo-item {
            background: white;
            border: 1px solid #ffcc02;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .previous-repo-item h4 {
            margin: 0 0 10px 0;
            color: #e65100;
        }
        .mo-scans {
            background: #f3e5f5;
            border: 1px solid #9c27b0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .mo-scans h3 {
            margin: 0 0 15px 0;
            color: #6a1b9a;
        }
        .scan-item {
            background: white;
            border: 1px solid #ce93d8;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        .scan-item strong {
            color: #6a1b9a;
        }
        .past-repos-table {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .past-repos-table h3 {
            margin: 0 0 15px 0;
            color: #2e7d32;
        }
        .repos-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .repos-table th {
            background: #4caf50;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9em;
        }
        .repos-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        .repos-table tr:hover {
            background: #f8f9fa;
        }
        .repos-table tr:last-child td {
            border-bottom: none;
        }
        .repo-type-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .repo-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .repo-status-active {
            background: #fff3e0;
            color: #f57c00;
        }
        .repo-status-completed {
            background: #e8f5e8;
            color: #388e3c;
        }
        .repo-status-cancelled {
            background: #ffebee;
            color: #d32f2f;
        }
        .customer-fault-badge {
            background: #ffebee;
            color: #d32f2f;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .no-repos-message {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .past-scans-table {
            background: #f3e5f5;
            border: 1px solid #9c27b0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .past-scans-table h3 {
            margin: 0 0 15px 0;
            color: #6a1b9a;
        }
        .scans-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .scans-table th {
            background: #9c27b0;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9em;
        }
        .scans-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        .scans-table tr:hover {
            background: #f8f9fa;
        }
        .scans-table tr:last-child td {
            border-bottom: none;
        }
        .scan-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .scan-status-active {
            background: #e8f5e8;
            color: #388e3c;
        }
        .scan-status-repo {
            background: #fff3e0;
            color: #f57c00;
        }
        .scan-status-manual {
            background: #e3f2fd;
            color: #1976d2;
        }
        .work-area-badge {
            background: #f3e5f5;
            color: #6a1b9a;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .no-scans-message {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .form-column {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-full-width {
            grid-column: 1 / -1;
        }
        .textarea-large {
            min-height: 120px;
            resize: vertical;
        }
        .textarea-notes {
            min-height: 100px;
            resize: vertical;
        }
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                padding: 10px 20px;
            }
            .nav-links {
                margin-top: 10px;
            }
            .nav-links a {
                padding: 10px 15px;
            }
            .repo-grid {
                grid-template-columns: 1fr;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
  `
}

// Helper function to generate pagination controls
function generatePagination(
  currentPage: number,
  totalPages: number,
  searchParams: any,
  baseUrl: string = '/create-repo'
): string {
  const { mo_order, customer, num } = searchParams

  // Build query string for search parameters
  const searchQuery = new URLSearchParams()
  if (mo_order) searchQuery.set('mo_order', mo_order)
  if (customer) searchQuery.set('customer', customer)
  if (num) searchQuery.set('num', num)

  const getPageUrl = (page: number) => {
    const query = new URLSearchParams(searchQuery)
    query.set('page', page.toString())
    return `${baseUrl}?${query.toString()}`
  }

  let pagination = '<div class="pagination">'

  // Previous button
  if (currentPage > 1) {
    pagination += `<a href="${getPageUrl(currentPage - 1)}" class="pagination-btn">← Previous</a>`
  } else {
    pagination += '<span class="pagination-btn disabled">← Previous</span>'
  }

  // Page numbers
  const startPage = Math.max(1, currentPage - 2)
  const endPage = Math.min(totalPages, currentPage + 2)

  if (startPage > 1) {
    pagination += `<a href="${getPageUrl(1)}" class="pagination-btn">1</a>`
    if (startPage > 2) {
      pagination += '<span class="pagination-btn disabled">...</span>'
    }
  }

  for (let i = startPage; i <= endPage; i++) {
    if (i === currentPage) {
      pagination += `<span class="pagination-btn active">${i}</span>`
    } else {
      pagination += `<a href="${getPageUrl(i)}" class="pagination-btn">${i}</a>`
    }
  }

  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      pagination += '<span class="pagination-btn disabled">...</span>'
    }
    pagination += `<a href="${getPageUrl(totalPages)}" class="pagination-btn">${totalPages}</a>`
  }

  // Next button
  if (currentPage < totalPages) {
    pagination += `<a href="${getPageUrl(currentPage + 1)}" class="pagination-btn">Next →</a>`
  } else {
    pagination += '<span class="pagination-btn disabled">Next →</span>'
  }

  pagination += '</div>'
  return pagination
}

export const getSelectMoPage = async (req: Request, res: Response) => {
  try {
    // Get search and pagination parameters from query string
    const { mo_order, customer, num, page } = req.query
    const currentPage = parseInt(page as string) || 1
    const limit = 50
    const offset = (currentPage - 1) * limit

    let result

    // If search parameters are provided, search; otherwise show recent MOs
    if (mo_order || customer || num) {
      result = await moService.searchMos({
        mo_order: mo_order as string,
        customer: customer as string,
        num: num as string,
        limit,
        offset,
      })
    } else {
      // Show available MOs (those without work repos)
      result = await moService.getAvailableMos(limit, offset)
    }

    const { data: mos, total, hasMore } = result
    const totalPages = Math.ceil(total / limit)

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Manufacturing Order - Repo Tracker</title>
    ${generateCommonCSS()}
</head>
<body>
    ${generateNavigation('create')}

    <div class="main-content">
        <div class="container">
            <div class="header">
                <h1>Select Manufacturing Order</h1>
                <p>Choose an MO that needs rework (reposition)</p>
            </div>

            <div class="content">
                <div class="search-form">
                    <form method="GET" action="/create-repo">
                        <div class="search-row">
                            <div class="form-group">
                                <label for="mo_order">Order Number:</label>
                                <input type="text" id="mo_order" name="mo_order" value="${mo_order || ''}" placeholder="Search by order number">
                            </div>
                            <div class="form-group">
                                <label for="customer">Customer:</label>
                                <input type="text" id="customer" name="customer" value="${customer || ''}" placeholder="Search by customer">
                            </div>
                            <div class="form-group">
                                <label for="num">MO Number:</label>
                                <input type="text" id="num" name="num" value="${num || ''}" placeholder="Search by MO number">
                            </div>
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn">Search</button>
                            </div>
                        </div>
                    </form>
                </div>

                ${
                  mos.length === 0
                    ? `
                    <div class="empty-state">
                        <h3>No Manufacturing Orders Found</h3>
                        <p>No MOs match your search criteria or all available MOs already have repositions.</p>
                        <a href="/create-repo" class="btn">Clear Search</a>
                    </div>
                `
                    : `
                    <table class="mo-table">
                        <thead>
                            <tr>
                                <th>MO ID</th>
                                <th>Order</th>
                                <th>MO Number</th>
                                <th>Customer</th>
                                <th>Style</th>
                                <th>Quantity</th>
                                <th>Status</th>
                                <th>Required Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${mos
                              .map(
                                (mo) => `
                                <tr>
                                    <td>${mo.mo_id}</td>
                                    <td>${mo.mo_order || 'N/A'}</td>
                                    <td>${mo.num}</td>
                                    <td>${mo.customer}</td>
                                    <td>${mo.style}</td>
                                    <td>${mo.quantity}</td>
                                    <td>${mo.mo_status || 'N/A'}</td>
                                    <td>${mo.required_date ? new Date(mo.required_date).toLocaleDateString() : 'N/A'}</td>
                                    <td>
                                        <a href="/create-repo/form?mo_id=${mo.mo_id}" class="select-btn">Select</a>
                                    </td>
                                </tr>
                            `
                              )
                              .join('')}
                        </tbody>
                    </table>

                    ${totalPages > 1 ? generatePagination(currentPage, totalPages, { mo_order, customer, num }) : ''}
                `
                }

                ${
                  mos.length > 0
                    ? `
                    <div class="pagination-info" style="text-align: center; margin-top: 20px;">
                        Showing ${offset + 1}-${Math.min(offset + limit, total)} of ${total} results
                    </div>
                `
                    : ''
                }
            </div>
        </div>
    </div>
</body>
</html>
    `

    res.status(StatusCodes.OK).send(html)
  } catch (error) {
    console.error('Error generating MO selection page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send('Error loading page')
  }
}

export const getCreateRepoPage = async (req: Request, res: Response) => {
  try {
    // Check if this is the form page with a selected MO
    const moId = req.query.mo_id as string

    if (moId) {
      // This is the form page - show the repo creation form with MO details
      return await getCreateRepoFormPage(req, res, parseInt(moId))
    } else {
      // This is the MO selection page
      return await getSelectMoPage(req, res)
    }
  } catch (error) {
    console.error('Error generating create repo page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send('Error loading page')
  }
}

export const getCreateRepoFormPage = async (
  req: Request,
  res: Response,
  moId: number
) => {
  try {
    // Get the selected MO, statuses, MO repos, all MO scans, and work areas
    const [selectedMo, statuses, moRepos, allMoScans, workAreas] =
      await Promise.all([
        moService.getMoById(moId),
        repoService.getAllStatuses(),
        repoService.getReposByMoId(moId), // Get all repos for this specific MO
        moService.getScansByMoId(moId), // Get all scans for this specific MO
        workAreaService.getActiveWorkAreas(),
      ])

    if (!selectedMo) {
      res.status(StatusCodes.NOT_FOUND).send('Manufacturing Order not found')
      return
    }

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Repository - Repo Tracker</title>
    ${generateCommonCSS()}
</head>
<body>
    ${generateNavigation('create')}

    <div class="main-content">
        <div class="container">
            <div class="header">
                <h1>Create New Reposition</h1>
                <p>Create a reposition (rework) for the selected manufacturing order</p>
            </div>

            <div class="content">
                <div class="mo-info">
                    <h3>Selected Manufacturing Order</h3>
                    <div class="mo-details">
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">MO ID</span>
                            <span class="mo-detail-value">${selectedMo.mo_id}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Order Number</span>
                            <span class="mo-detail-value">${selectedMo.mo_order || 'N/A'}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">MO Number</span>
                            <span class="mo-detail-value">${selectedMo.num}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Customer</span>
                            <span class="mo-detail-value">${selectedMo.customer}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Style</span>
                            <span class="mo-detail-value">${selectedMo.style}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Quantity</span>
                            <span class="mo-detail-value">${selectedMo.quantity}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Status</span>
                            <span class="mo-detail-value">${selectedMo.mo_status || 'N/A'}</span>
                        </div>
                        <div class="mo-detail-item">
                            <span class="mo-detail-label">Required Date</span>
                            <span class="mo-detail-value">${selectedMo.required_date ? new Date(selectedMo.required_date).toLocaleDateString() : 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <div class="past-repos-table">
                    <h3>Past Repositions for MO ${selectedMo.num}</h3>
                    ${
                      moRepos.length > 0
                        ? `
                        <table class="repos-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Reason</th>
                                    <th>Customer Fault</th>
                                    <th>Units Affected</th>
                                    <th>Printer</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${moRepos
                                  .map(
                                    (repo) => `
                                    <tr>
                                        <td>#${repo.id}</td>
                                        <td>
                                            ${repo.repo_type ? `<span class="repo-type-badge">${repo.repo_type}</span>` : 'N/A'}
                                        </td>
                                        <td>${repo.work_reason_note || 'N/A'}</td>
                                        <td>
                                            ${repo.customer_fault ? '<span class="customer-fault-badge">Yes</span>' : 'No'}
                                        </td>
                                        <td>${repo.affected_units || 'N/A'}</td>
                                        <td>${repo.printer_use || 'N/A'}</td>
                                        <td>${new Date(repo.created_at).toLocaleDateString()}</td>
                                    </tr>
                                `
                                  )
                                  .join('')}
                            </tbody>
                        </table>
                    `
                        : `
                        <div class="no-repos-message">
                            No past repositions found for this MO.
                        </div>
                    `
                    }
                </div>

                <div class="past-scans-table">
                    <h3>Past Scans for MO ${selectedMo.num}</h3>
                    ${
                      allMoScans.length > 0
                        ? `
                        <table class="scans-table">
                            <thead>
                                <tr>
                                    <th>Scan ID</th>
                                    <th>Task</th>
                                    <th>Supervisor</th>
                                    <th>Quantity</th>
                                    <th>Work Area</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${allMoScans
                                  .map(
                                    (scan) => `
                                    <tr>
                                        <td>#${scan.scan_id}</td>
                                        <td>${scan.task_name || 'N/A'}</td>
                                        <td>${scan.supervisor}</td>
                                        <td>${scan.quantity || 'N/A'}</td>
                                        <td>
                                            ${scan.work_area_id ? `<span class="work-area-badge">${scan.work_area_id}</span>` : 'N/A'}
                                        </td>
                                        <td>
                                            ${
                                              scan.is_repo
                                                ? '<span class="scan-status-badge scan-status-repo">Repo</span>'
                                                : scan.is_manual_change
                                                  ? '<span class="scan-status-badge scan-status-manual">Manual</span>'
                                                  : '<span class="scan-status-badge scan-status-active">Active</span>'
                                            }
                                        </td>
                                        <td>${new Date(scan.created_at).toLocaleDateString()}</td>
                                    </tr>
                                `
                                  )
                                  .join('')}
                            </tbody>
                        </table>
                    `
                        : `
                        <div class="no-scans-message">
                            No past scans found for this MO.
                        </div>
                    `
                    }
                </div>

                <form action="/api/v1/repos" method="POST">
                    <input type="hidden" name="mo_id" value="${selectedMo.mo_id}">

                    <div class="form-grid">
                        <!-- Left Column -->
                        <div class="form-column">
                            <div class="form-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="customer_fault" name="customer_fault" value="1">
                                    <label for="customer_fault">Customer Fault</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="units_affected">Units Affected:</label>
                                <input type="number" id="units_affected" name="units_affected" placeholder="Number of units affected" min="0">
                            </div>

                            <div class="form-group">
                                <label for="printer">Printer:</label>
                                <input type="text" id="printer" name="printer" placeholder="Printer name or identifier">
                            </div>

                            <div class="form-group">
                                <label for="items">Items:</label>
                                <textarea id="items" name="items" class="textarea-large" placeholder="List items that need rework or attention..."></textarea>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="form-column">
                            <div class="form-group">
                                <label for="repo_type">Reposition Type:</label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" id="type_corte" name="repo_type" value="Corte" required>
                                        <label for="type_corte">Corte</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="type_corte_sub" name="repo_type" value="Corte / Sub" required>
                                        <label for="type_corte_sub">Corte / Sub</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="type_full_process" name="repo_type" value="Full Process" required>
                                        <label for="type_full_process">Full Process</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="type_materials_only" name="repo_type" value="Materials Only" required>
                                        <label for="type_materials_only">Materials Only</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="work_reason_note">Rework Reason:</label>
                                <input type="text" id="work_reason_note" name="work_reason_note" placeholder="Describe the reason for this rework/reposition...">
                            </div>

                            <div class="form-group">
                                <label for="reported_work_areas">Reported Work Areas:</label>
                                <div class="multi-select-container">
                                    <input type="text" id="work_area_search" class="multi-select-input" placeholder="Search work areas..." autocomplete="off">
                                    <div id="work_area_dropdown" class="multi-select-dropdown">
                                        <!-- Options will be populated by JavaScript -->
                                    </div>
                                    <div class="selected-areas" id="selected_areas">
                                        <!-- Selected areas will appear here -->
                                    </div>
                                    <input type="hidden" id="reported_work_areas" name="reported_work_areas" value="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="materials">Materials:</label>
                                <div class="material-selection">
                                    <div id="material_entries">
                                        <!-- Material entry rows will be added here -->
                                    </div>
                                    <button type="button" class="add-material-btn" onclick="addMaterialRow()">+ Add Material</button>
                                    <button type="button" class="add-material-btn" onclick="loadMoMaterials()" style="margin-left: 10px; background: #2196f3;">Load MO Materials</button>
                                    <input type="hidden" id="materials_data" name="materials_data" value="">
                                    <textarea id="materials" name="materials" style="display: none;"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Full Width Notes Section -->
                        <div class="form-group form-full-width">
                            <label for="comments">Additional Notes:</label>
                            <textarea id="comments" name="comments" class="textarea-notes" placeholder="Additional comments, special instructions, or notes about this reposition..."></textarea>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-full-width">
                            <button type="submit" class="btn">Create Reposition</button>
                            <a href="/create-repo" class="btn btn-secondary" style="margin-left: 10px; text-decoration: none; display: inline-block;">Back to MO Selection</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Work Area Multi-Select Functionality
        const workAreas = ${JSON.stringify(workAreas)};
        let selectedAreas = [];

        const searchInput = document.getElementById('work_area_search');
        const dropdown = document.getElementById('work_area_dropdown');
        const selectedAreasContainer = document.getElementById('selected_areas');
        const hiddenInput = document.getElementById('reported_work_areas');

        function filterWorkAreas(searchTerm) {
            return workAreas.filter(area =>
                area.area_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                area.work_area_id.toString().includes(searchTerm)
            ).slice(0, 10); // Limit to 10 results
        }

        function renderDropdown(areas) {
            dropdown.innerHTML = '';
            areas.forEach(area => {
                const option = document.createElement('div');
                option.className = 'multi-select-option';
                option.innerHTML = \`
                    <input type="checkbox" id="area_\${area.work_area_id}"
                           \${selectedAreas.some(s => s.work_area_id === area.work_area_id) ? 'checked' : ''}>
                    <label for="area_\${area.work_area_id}">\${area.area_name} (ID: \${area.work_area_id})</label>
                \`;

                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    toggleArea(area);
                });

                dropdown.appendChild(option);
            });
        }

        function toggleArea(area) {
            const index = selectedAreas.findIndex(s => s.work_area_id === area.work_area_id);
            if (index > -1) {
                selectedAreas.splice(index, 1);
            } else {
                selectedAreas.push(area);
            }
            updateSelectedAreas();
            updateHiddenInput();
        }

        function updateSelectedAreas() {
            selectedAreasContainer.innerHTML = '';
            selectedAreas.forEach(area => {
                const tag = document.createElement('div');
                tag.className = 'selected-area-tag';
                tag.innerHTML = \`
                    \${area.area_name}
                    <span class="remove" onclick="removeArea(\${area.work_area_id})">×</span>
                \`;
                selectedAreasContainer.appendChild(tag);
            });
        }

        function removeArea(workAreaId) {
            selectedAreas = selectedAreas.filter(area => area.work_area_id !== workAreaId);
            updateSelectedAreas();
            updateHiddenInput();
            // Update dropdown checkboxes
            const filteredAreas = filterWorkAreas(searchInput.value);
            renderDropdown(filteredAreas);
        }

        function updateHiddenInput() {
            hiddenInput.value = selectedAreas.map(area => area.work_area_id).join(',');
        }

        // Event listeners
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value;
            const filteredAreas = filterWorkAreas(searchTerm);
            renderDropdown(filteredAreas);
            dropdown.classList.add('show');
        });

        searchInput.addEventListener('focus', () => {
            const filteredAreas = filterWorkAreas(searchInput.value);
            renderDropdown(filteredAreas);
            dropdown.classList.add('show');
        });

        document.addEventListener('click', (e) => {
            if (!e.target.closest('.multi-select-container')) {
                dropdown.classList.remove('show');
            }
        });

        // Initialize with empty search
        renderDropdown(filterWorkAreas(''));

        // Material Entry Functionality
        let materialRowCounter = 0;
        const materialsDataInput = document.getElementById('materials_data');
        const materialsTextarea = document.getElementById('materials');
        const materialEntriesContainer = document.getElementById('material_entries');

        async function searchMaterials(searchTerm) {
            try {
                const response = await fetch(\`/api/v1/materials/search?search=\${encodeURIComponent(searchTerm)}&limit=10\`);
                const data = await response.json();
                return data.success ? data.data : [];
            } catch (error) {
                console.error('Error searching materials:', error);
                return [];
            }
        }

        function addMaterialRow() {
            const rowId = materialRowCounter++;
            const row = document.createElement('div');
            row.className = 'material-entry-row';
            row.id = \`material-row-\${rowId}\`;

            row.innerHTML = \`
                <div class="form-group">
                    <label for="part_number_\${rowId}">Part Number:</label>
                    <div style="position: relative;">
                        <input type="text" id="part_number_\${rowId}" name="part_number_\${rowId}"
                               placeholder="Enter or search part number..."
                               onkeyup="handlePartNumberSearch(\${rowId}, this.value)"
                               onblur="hideSuggestions(\${rowId})">
                        <div id="suggestions_\${rowId}" class="material-search-suggestion">
                            <!-- Suggestions will appear here -->
                        </div>
                    </div>
                </div>
                <div class="form-group units-group">
                    <label for="units_\${rowId}">Units:</label>
                    <input type="text" id="units_\${rowId}" name="units_\${rowId}"
                           placeholder="Units" readonly>
                </div>
                <div class="form-group quantity-group">
                    <label for="quantity_\${rowId}">Quantity:</label>
                    <input type="number" id="quantity_\${rowId}" name="quantity_\${rowId}"
                           step="0.01" min="0.01" value="1.00" placeholder="Qty">
                </div>
                <div class="form-group action-group">
                    <button type="button" class="remove-material-btn" onclick="removeMaterialRow(\${rowId})">Remove</button>
                </div>
            \`;

            materialEntriesContainer.appendChild(row);
            updateMaterialsData();
        }

        function removeMaterialRow(rowId) {
            const row = document.getElementById(\`material-row-\${rowId}\`);
            if (row) {
                row.remove();
                updateMaterialsData();
            }
        }

        async function handlePartNumberSearch(rowId, searchTerm) {
            const suggestionsContainer = document.getElementById(\`suggestions_\${rowId}\`);

            if (searchTerm.length < 2) {
                suggestionsContainer.classList.remove('show');
                return;
            }

            try {
                const materials = await searchMaterials(searchTerm);
                renderSuggestions(rowId, materials);
            } catch (error) {
                console.error('Error searching materials:', error);
            }
        }

        function renderSuggestions(rowId, materials) {
            const suggestionsContainer = document.getElementById(\`suggestions_\${rowId}\`);

            if (materials.length === 0) {
                suggestionsContainer.classList.remove('show');
                return;
            }

            suggestionsContainer.innerHTML = '';
            materials.forEach(material => {
                const suggestion = document.createElement('div');
                suggestion.className = 'suggestion-item';
                suggestion.innerHTML = \`
                    <div class="suggestion-part-number">\${material.part_number}</div>
                    <div class="suggestion-unit">Unit: \${material.unit_of_measure || 'N/A'}</div>
                \`;

                suggestion.addEventListener('mousedown', (e) => {
                    e.preventDefault(); // Prevent blur event
                    selectSuggestion(rowId, material);
                });

                suggestionsContainer.appendChild(suggestion);
            });

            suggestionsContainer.classList.add('show');
        }

        function selectSuggestion(rowId, material) {
            const partNumberInput = document.getElementById(\`part_number_\${rowId}\`);
            const unitsInput = document.getElementById(\`units_\${rowId}\`);
            const suggestionsContainer = document.getElementById(\`suggestions_\${rowId}\`);

            partNumberInput.value = material.part_number;
            unitsInput.value = material.unit_of_measure || '';
            suggestionsContainer.classList.remove('show');
            updateMaterialsData();
        }

        function hideSuggestions(rowId) {
            setTimeout(() => {
                const suggestionsContainer = document.getElementById(\`suggestions_\${rowId}\`);
                if (suggestionsContainer) {
                    suggestionsContainer.classList.remove('show');
                }
            }, 200); // Delay to allow click events on suggestions
        }

        function updateMaterialsData() {
            const materials = [];
            const rows = materialEntriesContainer.querySelectorAll('.material-entry-row');

            rows.forEach(row => {
                const partNumberInput = row.querySelector('input[name^="part_number_"]');
                const unitsInput = row.querySelector('input[name^="units_"]');
                const quantityInput = row.querySelector('input[name^="quantity_"]');

                if (partNumberInput && quantityInput && partNumberInput.value.trim()) {
                    materials.push({
                        part_number: partNumberInput.value.trim(),
                        quantity: parseFloat(quantityInput.value) || 1.00,
                        unit_of_measure: unitsInput ? unitsInput.value.trim() || null : null
                    });
                }
            });

            // Update hidden field with JSON data
            materialsDataInput.value = JSON.stringify(materials);

            // Update textarea with formatted text for backward compatibility
            const materialsText = materials.map(material =>
                \`\${material.part_number} - Qty: \${material.quantity} \${material.unit_of_measure || ''}\`
            ).join('\\n');
            materialsTextarea.value = materialsText;
        }

        async function loadMoMaterials() {
            try {
                const response = await fetch(\`/api/v1/materials/mo-materials?moNum=\${encodeURIComponent('${selectedMo.num}')}&customer=\${encodeURIComponent('${selectedMo.customer}')}\`);
                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    // Clear existing rows
                    materialEntriesContainer.innerHTML = '';
                    materialRowCounter = 0;

                    // Add rows for each MO material
                    data.data.forEach(material => {
                        const rowId = materialRowCounter++;
                        const row = document.createElement('div');
                        row.className = 'material-entry-row';
                        row.id = \`material-row-\${rowId}\`;

                        row.innerHTML = \`
                            <div class="form-group">
                                <label for="part_number_\${rowId}">Part Number:</label>
                                <div style="position: relative;">
                                    <input type="text" id="part_number_\${rowId}" name="part_number_\${rowId}"
                                           placeholder="Enter or search part number..."
                                           value="\${material.part_number}"
                                           onkeyup="handlePartNumberSearch(\${rowId}, this.value)"
                                           onblur="hideSuggestions(\${rowId})">
                                    <div id="suggestions_\${rowId}" class="material-search-suggestion">
                                        <!-- Suggestions will appear here -->
                                    </div>
                                </div>
                            </div>
                            <div class="form-group units-group">
                                <label for="units_\${rowId}">Units:</label>
                                <input type="text" id="units_\${rowId}" name="units_\${rowId}"
                                       placeholder="Units" value="\${material.unit_symbol || ''}" readonly>
                            </div>
                            <div class="form-group quantity-group">
                                <label for="quantity_\${rowId}">Quantity:</label>
                                <input type="number" id="quantity_\${rowId}" name="quantity_\${rowId}"
                                       step="0.01" min="0.01" value="\${material.quantity_required || 1.00}" placeholder="Qty">
                            </div>
                            <div class="form-group action-group">
                                <button type="button" class="remove-material-btn" onclick="removeMaterialRow(\${rowId})">Remove</button>
                            </div>
                        \`;

                        materialEntriesContainer.appendChild(row);
                    });

                    updateMaterialsData();
                } else {
                    alert('No materials found for this MO or customer type.');
                }
            } catch (error) {
                console.error('Error loading MO materials:', error);
                alert('Error loading MO materials. Please try again.');
            }
        }

        // Make functions global for onclick handlers
        window.addMaterialRow = addMaterialRow;
        window.removeMaterialRow = removeMaterialRow;
        window.handlePartNumberSearch = handlePartNumberSearch;
        window.hideSuggestions = hideSuggestions;
        window.loadMoMaterials = loadMoMaterials;

        // Add initial material row
        addMaterialRow();
    </script>
</body>
</html>
    `

    res.status(StatusCodes.OK).send(html)
  } catch (error) {
    console.error('Error generating create repo page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send('Error loading page')
  }
}

export const getActiveReposPage = async (req: Request, res: Response) => {
  try {
    // Get pagination parameters
    const { page } = req.query
    const currentPage = parseInt(page as string) || 1
    const limit = 50
    const offset = (currentPage - 1) * limit

    // Get repos with pagination and all statuses
    const [repoResult, allStatuses] = await Promise.all([
      repoService.getAllRepos(limit, offset),
      repoService.getAllStatuses(),
    ])

    const { data: allRepos, total } = repoResult

    // Create a map of status IDs to status names
    const statusMap = new Map()
    allStatuses.forEach((status) => {
      statusMap.set(status.id, status.name)
    })

    // Filter for active repos (you might want to adjust this based on your status definitions)
    const activeRepos = allRepos.filter((repo) => {
      const statusName = statusMap.get(repo.work_repo_status_id)
      return (
        statusName &&
        !statusName.toLowerCase().includes('complete') &&
        !statusName.toLowerCase().includes('closed') &&
        !statusName.toLowerCase().includes('cancelled')
      )
    })

    const totalPages = Math.ceil(total / limit)

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Repositories - Repo Tracker</title>
    ${generateCommonCSS()}
</head>
<body>
    ${generateNavigation('active')}

    <div class="main-content">
        <div class="container">
            <div class="header">
                <h1>Active Repositions</h1>
                <p>Manufacturing orders currently being reworked</p>
            </div>

            <div class="content">
                ${
                  activeRepos.length === 0
                    ? `
                    <div class="empty-state">
                        <h3>No Active Repositions</h3>
                        <p>There are currently no active repositions in the system.</p>
                        <a href="/create-repo" class="btn">Create New Reposition</a>
                    </div>
                `
                    : `
                    <div class="repo-grid">
                        ${activeRepos
                          .map(
                            (repo) => `
                            <div class="repo-card">
                                <h3 class="repo-id">Reposition #${repo.id}</h3>
                                <p><strong>MO ID:</strong> ${repo.mo_id || 'N/A'}</p>
                                <p><strong>Creator:</strong> ${repo.creator_email || 'N/A'}</p>
                                <p><strong>Type:</strong> ${repo.repo_type || 'N/A'}</p>
                                <p><strong>Status:</strong>
                                    <span class="status-badge status-active">
                                        ${statusMap.get(repo.work_repo_status_id) || 'Unknown'}
                                    </span>
                                </p>
                                <p><strong>Created:</strong> ${new Date(repo.created_at).toLocaleDateString()}</p>
                                ${repo.work_reason_note ? `<p><strong>Reason:</strong> ${repo.work_reason_note.substring(0, 100)}${repo.work_reason_note.length > 100 ? '...' : ''}</p>` : ''}
                                <p><strong>Est. Cost:</strong> $${((repo.estimated_material_cost || 0) + (repo.estimated_labor_cost || 0)).toLocaleString()}</p>
                            </div>
                        `
                          )
                          .join('')}
                    </div>

                    ${totalPages > 1 ? generatePagination(currentPage, totalPages, {}, '/active-repos') : ''}
                `
                }

                ${
                  activeRepos.length > 0
                    ? `
                    <div class="pagination-info" style="text-align: center; margin-top: 20px;">
                        Showing ${offset + 1}-${Math.min(offset + limit, total)} of ${total} total repositions (${activeRepos.length} active)
                    </div>
                `
                    : ''
                }
            </div>
        </div>
    </div>
</body>
</html>
    `

    res.status(StatusCodes.OK).send(html)
  } catch (error) {
    console.error('Error generating active repos page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send('Error loading page')
  }
}

export const getPrepareReposPage = async (req: Request, res: Response) => {
  try {
    // Get pagination parameters
    const { page } = req.query
    const currentPage = parseInt(page as string) || 1
    const limit = 50
    const offset = (currentPage - 1) * limit

    // Get repos with pagination and all statuses
    const [repoResult, allStatuses] = await Promise.all([
      repoService.getAllRepos(limit, offset),
      repoService.getAllStatuses(),
    ])

    const { data: allRepos, total } = repoResult

    // Create a map of status IDs to status names
    const statusMap = new Map()
    allStatuses.forEach((status) => {
      statusMap.set(status.id, status.name)
    })

    // Filter for repos that need preparation (adjust logic based on your workflow)
    const prepareRepos = allRepos.filter((repo) => {
      const statusName = statusMap.get(repo.work_repo_status_id)
      return (
        (statusName &&
          (statusName.toLowerCase().includes('pending') ||
            statusName.toLowerCase().includes('waiting') ||
            statusName.toLowerCase().includes('prepare') ||
            statusName.toLowerCase().includes('ready'))) ||
        (!repo.warehouse_materials_preparred && repo.work_repo_status_id)
      )
    })

    const totalPages = Math.ceil(total / limit)

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prepare Repositories - Repo Tracker</title>
    ${generateCommonCSS()}
</head>
<body>
    ${generateNavigation('prepare')}

    <div class="main-content">
        <div class="container">
            <div class="header">
                <h1>Repositions Awaiting Preparation</h1>
                <p>Manufacturing orders that need materials preparation for rework</p>
            </div>

            <div class="content">
                ${
                  prepareRepos.length === 0
                    ? `
                    <div class="empty-state">
                        <h3>No Repositions Awaiting Preparation</h3>
                        <p>All repositions are either prepared or not yet ready for preparation.</p>
                        <a href="/active-repos" class="btn">View Active Repositions</a>
                    </div>
                `
                    : `
                    <div class="repo-grid">
                        ${prepareRepos
                          .map(
                            (repo) => `
                            <div class="repo-card">
                                <h3 class="repo-id">Reposition #${repo.id}</h3>
                                <p><strong>MO ID:</strong> ${repo.mo_id || 'N/A'}</p>
                                <p><strong>Creator:</strong> ${repo.creator_email || 'N/A'}</p>
                                <p><strong>Type:</strong> ${repo.repo_type || 'N/A'}</p>
                                <p><strong>Status:</strong>
                                    <span class="status-badge status-prepare">
                                        ${statusMap.get(repo.work_repo_status_id) || 'Unknown'}
                                    </span>
                                </p>
                                <p><strong>Materials Prepared:</strong>
                                    <span class="status-badge ${repo.warehouse_materials_preparred ? 'status-active' : 'status-pending'}">
                                        ${repo.warehouse_materials_preparred ? 'Yes' : 'No'}
                                    </span>
                                </p>
                                <p><strong>Created:</strong> ${new Date(repo.created_at).toLocaleDateString()}</p>
                                ${repo.work_reason_note ? `<p><strong>Reason:</strong> ${repo.work_reason_note.substring(0, 100)}${repo.work_reason_note.length > 100 ? '...' : ''}</p>` : ''}
                                <p><strong>Est. Material Cost:</strong> $${(repo.estimated_material_cost || 0).toLocaleString()}</p>
                            </div>
                        `
                          )
                          .join('')}
                    </div>

                    ${totalPages > 1 ? generatePagination(currentPage, totalPages, {}, '/prepare-repos') : ''}
                `
                }

                ${
                  prepareRepos.length > 0
                    ? `
                    <div class="pagination-info" style="text-align: center; margin-top: 20px;">
                        Showing ${offset + 1}-${Math.min(offset + limit, total)} of ${total} total repositions (${prepareRepos.length} awaiting preparation)
                    </div>
                `
                    : ''
                }
            </div>
        </div>
    </div>
</body>
</html>
    `

    res.status(StatusCodes.OK).send(html)
  } catch (error) {
    console.error('Error generating prepare repos page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send('Error loading page')
  }
}
