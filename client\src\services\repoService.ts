import { apiClient } from '../lib/api'
import type {
  WorkRepo,
  WorkRepoStatus,
  FullRepoData,
  ApiResponse,
  PaginationParams,
  CreateRepoForm,
  HomeStats,
  RepoSummary,
} from '../types'

export const repoService = {
  // Get all repositories with pagination
  async getAllRepos(
    params?: PaginationParams
  ): Promise<ApiResponse<WorkRepo[]>> {
    const response = await apiClient.get('/v1/repos', { params })
    return response.data
  },

  // Get repository by ID
  async getRepoById(id: number): Promise<WorkRepo> {
    const response = await apiClient.get(`/v1/repos/${id}`)
    return response.data
  },

  // Get repository with all related data
  async getRepoWithAllData(id: number): Promise<FullRepoData> {
    const response = await apiClient.get(`/v1/repos/${id}/full`)
    return response.data
  },

  // Get repository summary
  async getRepoSummary(id: number): Promise<RepoSummary> {
    const response = await apiClient.get(`/v1/repos/${id}/summary`)
    return response.data
  },

  // Create new repository
  async createRepo(data: CreateRepoForm): Promise<WorkRepo> {
    const response = await apiClient.post('/v1/repos', data)
    return response.data
  },

  // Update repository
  async updateRepo(id: number, data: Partial<WorkRepo>): Promise<WorkRepo> {
    const response = await apiClient.put(`/v1/repos/${id}`, data)
    return response.data
  },

  // Delete repository
  async deleteRepo(id: number): Promise<void> {
    await apiClient.delete(`/v1/repos/${id}`)
  },

  // Get all statuses
  async getAllStatuses(): Promise<WorkRepoStatus[]> {
    const response = await apiClient.get('/v1/repos/statuses')
    return response.data
  },

  // Get repositories by MO ID
  async getReposByMoId(moId: number): Promise<WorkRepo[]> {
    const response = await apiClient.get(`/v1/repos?mo_id=${moId}`)
    return response.data
  },

  // Get home page statistics
  async getHomeStats(): Promise<HomeStats> {
    // This endpoint doesn't exist yet, we'll need to create it or use existing data
    const allRepos = await this.getAllRepos({ limit: 100 })

    const repos = allRepos.data
    const recentRepos = repos.slice(0, 5)

    return {
      totalRepos: repos.length,
      activeRepos: repos.filter((repo) => repo.created_at).length, // Placeholder logic
      completedRepos: 0, // Placeholder
      pendingRepos: 0, // Placeholder
      recentRepos,
    }
  },
}
