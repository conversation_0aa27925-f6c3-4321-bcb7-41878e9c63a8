import React, { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { MoSelection } from '@components/MoSelection/MoSelection'
import { RepoForm } from '@components/RepoForm/RepoForm'

const CreateRepo: React.FC = () => {
  const [searchParams] = useSearchParams()
  const moId = searchParams.get('mo_id')
  const [selectedMoId, setSelectedMoId] = useState<number | null>(
    moId ? parseInt(moId) : null
  )

  if (selectedMoId) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Create Reposition</h1>
          <p className="text-gray-600">Create a new reposition for the selected manufacturing order</p>
        </div>
        <RepoForm moId={selectedMoId} onBack={() => setSelectedMoId(null)} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Create Reposition</h1>
        <p className="text-gray-600">Select a manufacturing order to create a reposition</p>
      </div>
      <MoSelection onSelect={setSelectedMoId} />
    </div>
  )
}

export default CreateRepo
