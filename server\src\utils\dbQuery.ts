import { db } from '../db/database'
import { sql } from 'kysely'

export class DatabaseQuery {
  
  async executeQuery(query: string): Promise<any> {
    try {
      console.log(`\n🔍 Executing query: ${query}`)
      console.log('-'.repeat(50))
      
      const result = await sql.raw(query).execute(db)
      
      if (result.rows && result.rows.length > 0) {
        console.log(`✅ Query executed successfully. Found ${result.rows.length} rows.`)
        console.log('\nResults:')
        console.table(result.rows)
      } else {
        console.log('✅ Query executed successfully. No rows returned.')
      }
      
      return result
    } catch (error) {
      console.error('❌ Error executing query:', error)
      throw error
    }
  }

  async showAllTables(): Promise<void> {
    await this.executeQuery('SHOW TABLES')
  }

  async showColumns(tableName: string): Promise<void> {
    await this.executeQuery(`SHOW COLUMNS FROM \`${tableName}\``)
  }

  async describeTable(tableName: string): Promise<void> {
    await this.executeQuery(`DESCRIBE \`${tableName}\``)
  }

  async showCreateTable(tableName: string): Promise<void> {
    await this.executeQuery(`SHOW CREATE TABLE \`${tableName}\``)
  }

  async countRows(tableName: string): Promise<void> {
    await this.executeQuery(`SELECT COUNT(*) as row_count FROM \`${tableName}\``)
  }

  async showSampleData(tableName: string, limit: number = 5): Promise<void> {
    await this.executeQuery(`SELECT * FROM \`${tableName}\` LIMIT ${limit}`)
  }
}

// Create and export an instance
export const dbQuery = new DatabaseQuery()

// CLI function for running predefined queries
export async function runQuickInspection() {
  try {
    console.log('🚀 Starting Quick Database Inspection...\n')
    
    // Get command line arguments
    const args = process.argv.slice(2)
    const command = args[0]
    const tableName = args[1]
    
    if (!command) {
      console.log('Usage examples:')
      console.log('  npm run db:query tables                    # Show all tables')
      console.log('  npm run db:query columns work_repos        # Show columns for work_repos')
      console.log('  npm run db:query describe work_repos       # Describe work_repos table')
      console.log('  npm run db:query create work_repos         # Show CREATE TABLE statement')
      console.log('  npm run db:query count work_repos          # Count rows in work_repos')
      console.log('  npm run db:query sample work_repos         # Show sample data from work_repos')
      console.log('  npm run db:query sql "SELECT * FROM work_repos LIMIT 3"  # Custom SQL')
      return
    }

    switch (command.toLowerCase()) {
      case 'tables':
        await dbQuery.showAllTables()
        break
        
      case 'columns':
        if (!tableName) {
          console.log('❌ Please provide a table name: npm run db:query columns <table_name>')
          return
        }
        await dbQuery.showColumns(tableName)
        break
        
      case 'describe':
        if (!tableName) {
          console.log('❌ Please provide a table name: npm run db:query describe <table_name>')
          return
        }
        await dbQuery.describeTable(tableName)
        break
        
      case 'create':
        if (!tableName) {
          console.log('❌ Please provide a table name: npm run db:query create <table_name>')
          return
        }
        await dbQuery.showCreateTable(tableName)
        break
        
      case 'count':
        if (!tableName) {
          console.log('❌ Please provide a table name: npm run db:query count <table_name>')
          return
        }
        await dbQuery.countRows(tableName)
        break
        
      case 'sample':
        if (!tableName) {
          console.log('❌ Please provide a table name: npm run db:query sample <table_name>')
          return
        }
        await dbQuery.showSampleData(tableName)
        break
        
      case 'sql':
        if (!tableName) {
          console.log('❌ Please provide a SQL query: npm run db:query sql "SELECT * FROM table_name"')
          return
        }
        await dbQuery.executeQuery(tableName) // tableName is actually the SQL query in this case
        break
        
      default:
        console.log(`❌ Unknown command: ${command}`)
        console.log('Available commands: tables, columns, describe, create, count, sample, sql')
    }
    
  } catch (error) {
    console.error('Failed to run database query:', error)
  } finally {
    // Close the database connection
    await db.destroy()
    process.exit(0)
  }
}

// If this file is run directly, execute the query tool
if (require.main === module) {
  runQuickInspection()
}
