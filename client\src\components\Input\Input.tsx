import React from 'react'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

export const Input: React.FC<InputProps> = ({
  error,
  className = '',
  ...props
}) => {
  const baseClasses = 'w-full px-3 py-2 border-2 rounded-lg focus:outline-none transition-colors duration-300'
  const stateClasses = error 
    ? 'border-danger-300 focus:border-danger-500' 
    : 'border-gray-200 focus:border-primary-500'
  
  const classes = `${baseClasses} ${stateClasses} ${className}`
  
  return (
    <div className="w-full">
      <input className={classes} {...props} />
      {error && (
        <p className="mt-1 text-sm text-danger-600">{error}</p>
      )}
    </div>
  )
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

export const Textarea: React.FC<TextareaProps> = ({
  error,
  className = '',
  ...props
}) => {
  const baseClasses = 'w-full px-3 py-2 border-2 rounded-lg focus:outline-none transition-colors duration-300 resize-none'
  const stateClasses = error 
    ? 'border-danger-300 focus:border-danger-500' 
    : 'border-gray-200 focus:border-primary-500'
  
  const classes = `${baseClasses} ${stateClasses} ${className}`
  
  return (
    <div className="w-full">
      <textarea className={classes} {...props} />
      {error && (
        <p className="mt-1 text-sm text-danger-600">{error}</p>
      )}
    </div>
  )
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: string
}

export const Select: React.FC<SelectProps> = ({
  error,
  className = '',
  children,
  ...props
}) => {
  const baseClasses = 'w-full px-3 py-2 border-2 rounded-lg focus:outline-none transition-colors duration-300'
  const stateClasses = error 
    ? 'border-danger-300 focus:border-danger-500' 
    : 'border-gray-200 focus:border-primary-500'
  
  const classes = `${baseClasses} ${stateClasses} ${className}`
  
  return (
    <div className="w-full">
      <select className={classes} {...props}>
        {children}
      </select>
      {error && (
        <p className="mt-1 text-sm text-danger-600">{error}</p>
      )}
    </div>
  )
}
