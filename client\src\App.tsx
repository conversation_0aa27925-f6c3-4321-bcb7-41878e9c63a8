
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Suspense, lazy } from 'react'
import Layout from './components/Layout'
import LoadingSpinner from './components/LoadingSpinner'
import './App.css'

// Lazy load page components
const Home = lazy(() => import('./pages/Home'))
const CreateRepo = lazy(() => import('./pages/CreateRepo'))
const ActiveRepos = lazy(() => import('./pages/ActiveRepos'))
const PrepareRepos = lazy(() => import('./pages/PrepareRepos'))

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Layout>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/create-repo" element={<CreateRepo />} />
              <Route path="/active-repos" element={<ActiveRepos />} />
              <Route path="/prepare-repos" element={<PrepareRepos />} />
            </Routes>
          </Suspense>
        </Layout>
      </Router>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}

export default App
