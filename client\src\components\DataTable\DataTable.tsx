import { type ReactNode } from 'react'
import './DataTable.css'

export interface Column<T> {
  key: string
  dataIndex?: keyof T
  header: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  render?: (value: any, row: T) => ReactNode
  className?: string
  width?: string
}

export interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  keyField: keyof T
  className?: string
  minWidth?: string
  emptyMessage?: string
  loading?: boolean
  loadingMessage?: string
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  keyField,
  className = '',
  minWidth = '600px',
  emptyMessage = 'No data available',
  loading = false,
  loadingMessage = 'Loading...'
}: DataTableProps<T>) => {
  if (loading) {
    return (
      <div className="data-table-loading">
        <div className="spinner"></div>
        <p>{loadingMessage}</p>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="data-table-empty">
        <p>{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={`data-table-container ${className}`}>
      <table className="data-table" style={{ minWidth }}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th
                key={column.key?.toString()}
                className={column.className}
                style={{ width: column.width }}
              >
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row) => (
            <tr key={String(row[keyField])}>
              {columns.map((column) => {
                const key = column.dataIndex || column.key as keyof T
                const value = key && row[key] ? row[key] : null
                return (
                  <td key={String(column.key)} className={column.className}>
                    {column.render
                      ? column.render(value, row)
                      : String(value) || 'N/A'}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default DataTable
