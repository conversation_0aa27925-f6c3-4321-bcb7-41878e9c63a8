import { type ReactNode } from 'react'
import './DataTable.css'

export interface Column<T> {
  key: keyof T | string
  header: string
  render?: (value: T[keyof T], row: T) => ReactNode
  className?: string
  width?: string
}

export interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  keyField: keyof T
  className?: string
  minWidth?: string
  emptyMessage?: string
  loading?: boolean
  loadingMessage?: string
}

const DataTable = <T,>({
  data,
  columns,
  keyField,
  className = '',
  minWidth = '600px',
  emptyMessage = 'No data available',
  loading = false,
  loadingMessage = 'Loading...'
}: DataTableProps<T>) => {
  if (loading) {
    return (
      <div className="data-table-loading">
        <div className="spinner"></div>
        <p>{loadingMessage}</p>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="data-table-empty">
        <p>{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={`data-table-container ${className}`}>
      <table className="data-table" style={{ minWidth }}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th
                key={column.key?.toString()}
                className={column.className}
                style={{ width: column.width }}
              >
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row) => (
            <tr key={row[keyField]?.toString()}>
              {columns.map((column) => (
                <td key={column.key?.toString()} className={column.className}>
                  {column.render
                    ? column.render(row[column.key], row)
                    : row[column.key]?.toString() || 'N/A'}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default DataTable
