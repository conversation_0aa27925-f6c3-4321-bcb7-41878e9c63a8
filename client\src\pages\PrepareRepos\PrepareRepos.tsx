import React from 'react'
import { useRepos } from '@hooks/useRepos'
import { DataTable, type Column } from '@components/DataTable/DataTable'

interface Repo {
  id: number
  creator_email?: string
  repo_type?: string
  customer_fault?: boolean
  units_affected?: number
  created_at: string
}

const PrepareRepos: React.FC = () => {
  const { data, isLoading, error } = useRepos({ limit: 100 })

  // Define table columns
  const columns: Column<Repo>[] = [
    {
      key: 'action',
      header: 'Actions',
      className: 'action-column',
      render: () => (
        <button className="btn-action btn-prepare">
          Prepare
        </button>
      )
    },
    {
      key: 'id',
      header: 'ID',
      render: (value) => `#${value}`
    },
    {
      key: 'creator_email',
      header: 'Creator'
    },
    {
      key: 'repo_type',
      header: 'Type'
    },
    {
      key: 'customer_fault',
      header: 'Customer Fault',
      render: (value) => (
        <span className={`badge ${value ? 'badge-yes' : 'badge-no'}`}>
          {value ? 'Yes' : 'No'}
        </span>
      )
    },
    {
      key: 'units_affected',
      header: 'Units Affected'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
        <p className="text-gray-600">Loading repositions awaiting preparation...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Prepare Repositions</h2>
        <p className="text-gray-600">Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  // Filter repos that need preparation (this logic would be refined based on actual status)
  const repos = data?.data || []
  const prepareRepos = repos.filter(repo =>
    // This is placeholder logic - in reality, you'd filter by status
    repo.created_at && !repo.notes
  )

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Repositions Awaiting Preparation</h1>
        <p className="text-gray-600">Manufacturing orders that need materials preparation for rework</p>
      </div>

      <div className="card">
        <DataTable
          data={prepareRepos}
          columns={columns}
          keyField="id"
          minWidth="600px"
          emptyMessage="All repositions are either completed or do not require preparation at this time."
        />
      </div>
    </div>
  )
}

export default PrepareRepos
