import React from 'react'

export interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showInfo?: boolean
  total?: number
  limit?: number
  className?: string
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showInfo = true,
  total,
  limit,
  className = ''
}) => {
  if (totalPages <= 1) {
    return null
  }

  const offset = (currentPage - 1) * (limit || 0)
  const startItem = offset + 1
  const endItem = Math.min(offset + (limit || 0), total || 0)

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        <span className="text-sm text-gray-700 font-medium">
          Page {currentPage} of {totalPages}
        </span>

        <div className="flex space-x-2">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200 ${currentPage === 1
                ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
          >
            Previous
          </button>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200 ${currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
          >
            Next
          </button>
        </div>
      </div>

      {showInfo && total && limit && (
        <div className="text-center text-sm text-gray-600">
          Showing {startItem}-{endItem} of {total} results
        </div>
      )}
    </div>
  )
}
