import React from 'react'
import './Pagination.css'

export interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showInfo?: boolean
  total?: number
  limit?: number
  className?: string
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showInfo = true,
  total,
  limit,
  className = ''
}) => {
  if (totalPages <= 1) {
    return null
  }

  const offset = (currentPage - 1) * (limit || 0)
  const startItem = offset + 1
  const endItem = Math.min(offset + (limit || 0), total || 0)

  return (
    <>
      <div className={`pagination ${className}`}>
        <span className="page-info">
          Page {currentPage} of {totalPages}
        </span>
        
        <div className="pagination-nav">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="btn-page"
          >
            Previous
          </button>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="btn-page"
          >
            Next
          </button>
        </div>
      </div>

      {showInfo && total && limit && (
        <div className="pagination-info">
          Showing {startItem}-{endItem} of {total} results
        </div>
      )}
    </>
  )
}

export default Pagination
