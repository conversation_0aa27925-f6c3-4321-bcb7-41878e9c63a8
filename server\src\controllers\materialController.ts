import { Request, Response } from 'express'
import { StatusCodes, ReasonPhrases } from 'http-status-codes'
import { materialService } from '../services/materialService'
import { z } from 'zod'

// Schema for material search query parameters
const materialSearchSchema = z.object({
  search: z.string().optional(),
  limit: z.coerce.number().int().positive().max(50).default(10),
})

// Schema for MO materials query parameters
const moMaterialsSchema = z.object({
  moNum: z.string().min(1, 'MO number is required'),
  customer: z.string().min(1, 'Customer is required'),
})

export const searchMaterials = async (req: Request, res: Response) => {
  try {
    const { search, limit } = materialSearchSchema.parse(req.query)

    let materials
    if (search && search.trim()) {
      materials = await materialService.searchMaterials(search.trim(), limit)
    } else {
      materials = await materialService.getPopularMaterials(limit)
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: materials,
      total: materials.length,
      page: 1,
      limit: limit,
      totalPages: 1,
      hasMore: false,
    })
  } catch (error) {
    console.error('Error searching materials:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getMaterialByPartNumber = async (req: Request, res: Response) => {
  try {
    const { partNumber } = req.params

    if (!partNumber) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Part number is required',
      })
      return
    }

    const material = await materialService.getMaterialByPartNumber(partNumber)

    if (!material) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Material not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: material,
    })
  } catch (error) {
    console.error('Error getting material:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getMoMaterials = async (req: Request, res: Response) => {
  try {
    const { moNum, customer } = moMaterialsSchema.parse(req.query)

    const materials = await materialService.getMoMaterials(moNum, customer)

    res.status(StatusCodes.OK).json({
      success: true,
      data: materials,
      total: materials.length,
      page: 1,
      limit: materials.length,
      totalPages: 1,
      hasMore: false,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid query parameters',
        errors: error.errors,
      })
      return
    }

    console.error('Error getting MO materials:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
