import { db } from '../db/database'
import { getMoPartNumbersByMo } from '../librarys/poly_restful'

export interface MaterialSearchResult {
  part_number: string
  unit_of_measure: string | null
  usage_count: number
}

export interface MoMaterial {
  part_number: string
  unit_symbol: string
  quantity_required?: number
  description?: string
}

export class MaterialService {
  async searchMaterials(
    searchTerm: string,
    limit: number = 10
  ): Promise<MaterialSearchResult[]> {
    // Search for unique part numbers from existing work_repo_materials
    const results = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count'),
      ])
      .where('part_number', 'is not', null)
      .where('part_number', 'like', `%${searchTerm}%`)
      .groupBy(['part_number', 'unit_of_measure'])
      .orderBy('usage_count', 'desc')
      .orderBy('part_number', 'asc')
      .limit(limit)
      .execute()

    return results.map((result) => ({
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count),
    }))
  }

  async getPopularMaterials(
    limit: number = 10
  ): Promise<MaterialSearchResult[]> {
    // Get most commonly used materials
    const results = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count'),
      ])
      .where('part_number', 'is not', null)
      .groupBy(['part_number', 'unit_of_measure'])
      .orderBy('usage_count', 'desc')
      .orderBy('part_number', 'asc')
      .limit(limit)
      .execute()

    return results.map((result) => ({
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count),
    }))
  }

  async getMaterialByPartNumber(
    partNumber: string
  ): Promise<MaterialSearchResult | undefined> {
    const result = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count'),
      ])
      .where('part_number', '=', partNumber)
      .groupBy(['part_number', 'unit_of_measure'])
      .executeTakeFirst()

    if (!result) return undefined

    return {
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count),
    }
  }

  async getMoMaterials(moNum: string, customer: string): Promise<MoMaterial[]> {
    try {
      if (customer === 'ADIDAS') {
        // Use material_allocations table for ADIDAS
        const materials = await db
          .selectFrom('material_allocations')
          .select([
            'part_number',
            'unit_symbol',
            'quantity_required',
            'component_name',
          ])
          .where('num', '=', moNum)
          .execute()

        return materials.map((material) => ({
          part_number: material.part_number,
          unit_symbol: material.unit_symbol,
          quantity_required: Number(material.quantity_required),
          description: material.component_name,
        }))
      } else if (customer !== 'VARSITY') {
        // Use poly_restful service for other customers (except VARSITY)
        const polyMaterials = await getMoPartNumbersByMo(moNum)

        return polyMaterials.map((material) => ({
          part_number: material.PartNumber,
          unit_symbol: material.DatabaseUnits,
          quantity_required: parseFloat(material.QuantityRequired) || 0,
          description: material.Description,
        }))
      }

      // For VARSITY or if no specific logic, return empty array
      return []
    } catch (error) {
      console.error('Error getting MO materials:', error)
      return []
    }
  }
}

// Export a singleton instance
export const materialService = new MaterialService()
