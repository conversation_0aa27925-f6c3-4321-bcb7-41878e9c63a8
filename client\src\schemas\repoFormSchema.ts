import { z } from 'zod'

export const repoFormSchema = z.object({
  creator_email: z
    .string()
    .min(1, 'Creator email is required')
    .email('Please enter a valid email address'),
  
  repo_type: z
    .string()
    .min(1, 'Reposition type is required'),
  
  customer_fault: z
    .boolean()
    .default(false),
  
  units_affected: z
    .number()
    .min(0, 'Units affected must be 0 or greater')
    .default(0),
  
  items: z
    .string()
    .optional(),
  
  materials: z
    .string()
    .optional(),
  
  printer: z
    .string()
    .optional(),
  
  rework_reason: z
    .string()
    .optional(),
  
  notes: z
    .string()
    .optional(),
  
  work_areas: z
    .array(z.number())
    .optional()
    .default([])
})

export type RepoFormData = z.infer<typeof repoFormSchema>
