import express, { NextFunction, Request, Response } from 'express'
import { v1Router } from './v1/index'
// import { apiAuthMiddleware } from '../middleware/authMiddleware' // Commented out for now
import { v4 as uuidv4 } from 'uuid'
// import { db } from '@app/db/database'

export const apiRouter = express.Router()

export interface ApiResponse extends Response {
  locals: {
    request_uuid: string
  }
}

const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now()
  const requestUuid = uuidv4()
  res.locals.request_uuid = requestUuid

  console.log('Request UUID:', requestUuid)

  const originalSend = res.send
  // @ts-ignore
  res.send = function (responseData) {
    if (typeof responseData != null && typeof responseData === 'object') {
      // res.send will be called a second time with a string later so ignore for now
      // @ts-ignore
      return originalSend.apply(this, arguments)
    }
    let isJson = false
    if (responseData && typeof responseData === 'string') {
      try {
        JSON.parse(responseData)
        isJson = true
        console.log('responseData parsed')
      } catch (error) {}
    }
    try {
      const endTime = Date.now()
      const duration = endTime - startTime
      const ip = (
        (req.headers['x-real-ip'] && Array.isArray(req.headers['x-real-ip'])
          ? req.headers['x-real-ip'].join(',')
          : req.headers['x-real-ip']) ||
        req.socket.remoteAddress ||
        req.ip ||
        ''
      )
        .split(',')[0]
        .trim()
      const saveData = {
        request_uuid: requestUuid,
        ip_address: ip,
        method: req.method,
        url: req.originalUrl,
        headers: JSON.stringify(req.headers),
        body: JSON.stringify(req.body),
        response: isJson ? responseData : JSON.stringify(responseData),
        status_code: res.statusCode,
        duration_ms: duration,
        //customer_app_id: res.locals?.customer_app_id,
      }
      // console.log('log data', saveData)

      // db.insertInto('api_requests').values(saveData).execute()
    } catch (error) {
      console.error('Error saving request log:', error)
    }

    // @ts-ignore
    originalSend.apply(this, arguments)
  }

  next()
}

apiRouter.use(requestLogger)

apiRouter.use(express.json())
// apiRouter.use(apiAuthMiddleware)

apiRouter.get('/', (_req: Request, res: Response) => {
  res.send('Welcome to the API')
})

apiRouter.use('/v1', v1Router)
