import React from 'react'
import { useRepos } from '@hooks/useRepos'
import { DataTable, type Column } from '@components'
import './PrepareRepos.css'

interface Repo {
  id: number
  creator_email?: string
  repo_type?: string
  customer_fault?: boolean
  units_affected?: number
  created_at: string
}

const PrepareRepos: React.FC = () => {
  const { data, isLoading, error } = useRepos({ limit: 100 })

  // Define table columns
  const columns: Column<Repo>[] = [
    {
      key: 'action',
      header: 'Actions',
      className: 'action-column',
      render: () => (
        <button className="btn-action btn-prepare">
          Prepare
        </button>
      )
    },
    {
      key: 'id',
      header: 'ID',
      render: (value) => `#${value}`
    },
    {
      key: 'creator_email',
      header: 'Creator'
    },
    {
      key: 'repo_type',
      header: 'Type'
    },
    {
      key: 'customer_fault',
      header: 'Customer Fault',
      render: (value) => (
        <span className={`badge ${value ? 'yes' : 'no'}`}>
          {value ? 'Yes' : 'No'}
        </span>
      )
    },
    {
      key: 'units_affected',
      header: 'Units Affected'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading repositions awaiting preparation...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="error">
        <h2>Error Loading Prepare Repositions</h2>
        <p>Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  // Filter repos that need preparation (this logic would be refined based on actual status)
  const repos = data?.data || []
  const prepareRepos = repos.filter(repo =>
    // This is placeholder logic - in reality, you'd filter by status
    repo.created_at && !repo.notes
  )

  return (
    <div className="prepare-repos">
      <div className="header">
        <h1>Repositions Awaiting Preparation</h1>
        <p>Manufacturing orders that need materials preparation for rework</p>
      </div>

      <DataTable
        data={prepareRepos}
        columns={columns}
        keyField="id"
        minWidth="600px"
        emptyMessage="All repositions are either completed or do not require preparation at this time."
      />
    </div>
  )
}

export default PrepareRepos
