import React from 'react'
import { useRepos } from '../hooks/useRepos'
import './PrepareRepos.css'

const PrepareRepos: React.FC = () => {
  const { data, isLoading, error } = useRepos({ limit: 100 })

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading repositions awaiting preparation...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="error">
        <h2>Error Loading Prepare Repositions</h2>
        <p>Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  // Filter repos that need preparation (this logic would be refined based on actual status)
  const repos = data?.data || []
  const prepareRepos = repos.filter(repo => 
    // This is placeholder logic - in reality, you'd filter by status
    repo.created_at && !repo.notes
  )

  return (
    <div className="prepare-repos">
      <div className="header">
        <h1>Repositions Awaiting Preparation</h1>
        <p>Manufacturing orders that need materials preparation for rework</p>
      </div>

      {prepareRepos.length === 0 ? (
        <div className="empty-state">
          <h3>No Repositions Awaiting Preparation</h3>
          <p>All repositions are either completed or do not require preparation at this time.</p>
        </div>
      ) : (
        <div className="repos-table-container">
          <table className="repos-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>MO ID</th>
                <th>Creator</th>
                <th>Type</th>
                <th>Customer Fault</th>
                <th>Units Affected</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {prepareRepos.map((repo) => (
                <tr key={repo.id}>
                  <td>#{repo.id}</td>
                  <td>{repo.mo_id}</td>
                  <td>{repo.creator_email || 'N/A'}</td>
                  <td>{repo.repo_type || 'N/A'}</td>
                  <td>
                    <span className={`badge ${repo.customer_fault ? 'yes' : 'no'}`}>
                      {repo.customer_fault ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td>{repo.units_affected || 'N/A'}</td>
                  <td>{new Date(repo.created_at).toLocaleDateString()}</td>
                  <td>
                    <button className="btn-prepare">Prepare</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default PrepareRepos
