import React, { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { MoSelection, RepoForm } from '@components'
import './CreateRepo.css'

const CreateRepo: React.FC = () => {
  const [searchParams] = useSearchParams()
  const moId = searchParams.get('mo_id')
  const [selectedMoId, setSelectedMoId] = useState<number | null>(
    moId ? parseInt(moId) : null
  )

  if (selectedMoId) {
    return (
      <div className="create-repo">
        <div className="header">
          <h1>Create Reposition</h1>
          <p>Create a new reposition for the selected manufacturing order</p>
        </div>
        <RepoForm moId={selectedMoId} onBack={() => setSelectedMoId(null)} />
      </div>
    )
  }

  return (
    <div className="create-repo">
      <div className="header">
        <h1>Create Reposition</h1>
        <p>Select a manufacturing order to create a reposition</p>
      </div>
      <MoSelection onSelect={setSelectedMoId} />
    </div>
  )
}

export default CreateRepo
