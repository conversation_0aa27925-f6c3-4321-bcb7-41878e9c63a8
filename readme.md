# Repository Tracking System

A full-stack application for managing manufacturing order repositions with a React frontend and Node.js backend.

## Project Structure

```
repo_tracking/
├── client/                 # React frontend application
│   ├── src/
│   │   ├── components/     # Reusable React components
│   │   ├── pages/          # Page components (Home, CreateRepo, etc.)
│   │   ├── hooks/          # React Query hooks
│   │   ├── services/       # API service functions
│   │   ├── types/          # TypeScript type definitions
│   │   └── lib/            # Utility libraries (API client)
│   ├── public/             # Static assets
│   └── package.json        # Client dependencies
├── server/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Route handlers
│   │   ├── routes/         # API route definitions
│   │   ├── services/       # Business logic
│   │   ├── db/             # Database configuration
│   │   ├── middleware/     # Express middleware
│   │   └── utils/          # Utility functions
│   ├── public/             # Static files served by server
│   └── package.json        # Server dependencies
└── package.json            # Root package.json for scripts
```

## Technology Stack

### Frontend (Client)

- **React 18** with TypeScript
- **Vite** for build tooling and development server
- **React Router** for client-side routing
- **TanStack Query (React Query)** for data fetching and caching
- **Axios** for HTTP requests
- **CSS3** with modern responsive design

### Backend (Server)

- **Node.js** with TypeScript
- **Express.js** web framework
- **MySQL** database with **Kysely** query builder
- **CORS** for cross-origin requests
- **Zod** for schema validation
- **dotenv** for environment configuration

## Quick Start

### Installation

1. **Install all dependencies**

   ```bash
   npm run install:all
   ```

   Or install individually:

   ```bash
   # Install root dependencies (concurrently)
   npm install

   # Install server dependencies
   npm run install:server

   # Install client dependencies
   npm run install:client
   ```

2. **Environment Configuration**

   Create `.env` files:

   **Server (.env in server/ folder):**

   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASS=your_db_password
   DB_PORT=3306
   DB_NAME=your_database_name

   # Application Configuration
   NODE_ENV=development
   PORT=3230

   # CORS Configuration
   # Leave blank for development (allows any localhost port)
   # In production, set to comma-delimited list of allowed domains
   CORS_ORIGINS=
   ```

   **Client (.env in client/ folder):**

   ```env
   VITE_API_BASE_URL=http://localhost:3230/api
   ```

### Development

**Run both client and server concurrently:**

```bash
npm run dev
```

**Run individually:**

```bash
# Server only (http://localhost:3230)
npm run dev:server

# Client only (http://localhost:5173)
npm run dev:client
```

### Building for Production

```bash
# Build both client and server
npm run build

# Build individually
npm run build:server
npm run build:client
```

### Running in Production

```bash
npm start
```

## Available Scripts

- `npm run dev` - Run both server and client concurrently
- `npm run dev:full` - Run both server and client concurrently (alias for dev)
- `npm run dev:server` - Start server in development mode
- `npm run dev:client` - Start client development server
- `npm run build` - Build both server and client for production
- `npm run build:server` - Build server only
- `npm run build:client` - Build client only
- `npm run install:all` - Install all dependencies
- `npm run install:server` - Install server dependencies
- `npm run install:client` - Install client dependencies
- `npm start` - Start production server
- `npm run db:inspect` - Inspect database
- `npm run db:query` - Run database queries

## URLs

- **Server**: http://localhost:3230
- **Client**: http://localhost:5173
- **API Documentation**: http://localhost:3230/api/v1 (API endpoints)

## Features

### Current Features

- **Home Dashboard** - Repository statistics and quick actions
- **Create Reposition** - Multi-step form for creating new repositions
- **Active Repositories** - View and manage active repositions
- **Prepare Repositories** - Manage repositories awaiting preparation
- **Responsive Design** - Works on desktop and mobile devices
- **Real-time Data** - React Query for efficient data fetching and caching

### Planned Features

- Repository detail views
- Advanced search and filtering
- Material management
- Work area assignments
- Status tracking
- Reporting and analytics

## API Endpoints

### Repositories

- `GET /api/v1/repos` - Get all repositories
- `GET /api/v1/repos/:id` - Get repository by ID
- `POST /api/v1/repos` - Create new repository
- `PUT /api/v1/repos/:id` - Update repository
- `DELETE /api/v1/repos/:id` - Delete repository

### Materials

- `GET /api/v1/materials/search` - Search materials
- `GET /api/v1/materials/mo-materials` - Get materials for MO

### Additional endpoints for MOs and work areas (in development)

## CORS Configuration

The server uses environment-based CORS configuration for maximum flexibility:

### Development Mode
- **`CORS_ORIGINS`** is blank or not set
- Allows any `localhost` or `127.0.0.1` port
- Perfect for development when Vite picks random ports

### Production Mode
- **`CORS_ORIGINS`** contains comma-delimited allowed domains
- Example: `CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com`
- Only specified domains are allowed for security

### Benefits
- **Automatic Development Setup**: No need to update CORS when ports change
- **Production Security**: Strict domain validation in production
- **Easy Configuration**: Single environment variable controls everything

## Database Schema

The application uses MySQL with tables for:
- `work_repos` - Main repository records
- `mo_numbers` - Manufacturing order information
- `work_repo_materials` - Materials associated with repositories
- `work_repo_pieces` - Pieces/components for repositories
- `work_areas` - Available work areas
- `material_allocations` - Material inventory
