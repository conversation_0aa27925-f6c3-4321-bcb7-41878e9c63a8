import { useQuery } from '@tanstack/react-query'
import { materialService } from '../services/materialService'
import { workAreaService } from '../services/workAreaService'
import type { MaterialSearchParams } from '../types'

// Query keys
export const materialKeys = {
  all: ['materials'] as const,
  search: (params: MaterialSearchParams) => [...materialKeys.all, 'search', params] as const,
  moMaterials: (moId: number, customer?: string) => [...materialKeys.all, 'moMaterials', moId, customer] as const,
  detail: (partNumber: string) => [...materialKeys.all, 'detail', partNumber] as const,
}

export const workAreaKeys = {
  all: ['workAreas'] as const,
  active: () => [...workAreaKeys.all, 'active'] as const,
}

// Material hooks
export const useSearchMaterials = (params: MaterialSearchParams) => {
  return useQuery({
    queryKey: materialKeys.search(params),
    queryFn: () => materialService.searchMaterials(params),
    enabled: !!params.query && params.query.length > 0,
  })
}

export const useMoMaterials = (moId: number, customer?: string) => {
  return useQuery({
    queryKey: materialKeys.moMaterials(moId, customer),
    queryFn: () => materialService.getMoMaterials(moId, customer),
    enabled: !!moId,
  })
}

export const useMaterial = (partNumber: string) => {
  return useQuery({
    queryKey: materialKeys.detail(partNumber),
    queryFn: () => materialService.getMaterialByPartNumber(partNumber),
    enabled: !!partNumber,
  })
}

// Work area hooks
export const useActiveWorkAreas = () => {
  return useQuery({
    queryKey: workAreaKeys.active(),
    queryFn: () => workAreaService.getActiveWorkAreas(),
  })
}

export const useAllWorkAreas = () => {
  return useQuery({
    queryKey: workAreaKeys.all,
    queryFn: () => workAreaService.getAllWorkAreas(),
  })
}
