<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Varpro Bifrost API Documentation</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
    />
  </head>
  <body>
    <div class="container">
      <!-- Left Navigation -->
      <nav class="sidebar">
        <div class="sidebar-header">
          <h1>Varpro Bifrost API</h1>
          <p class="version">v0.1.6</p>
        </div>

        <div class="nav-section">
          <h3>Getting Started</h3>
          <ul>
            <li><a href="#introduction" class="nav-link">Introduction</a></li>
            <li>
              <a href="#authentication" class="nav-link">Authentication</a>
            </li>
            <li><a href="#errors" class="nav-link">Error Handling</a></li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>Orders</h3>
          <ul>
            <li>
              <a href="#order-endpoints" class="nav-link">Order Details</a>
              <ul class="sub-nav">
                <li>
                  <a href="#order-response-schema" class="nav-link"
                    >Order Details Object</a
                  >
                </li>
                <li>
                  <a href="#order-details" class="nav-link"
                    >Get Order Details</a
                  >
                </li>
              </ul>
            </li>
          </ul>
        </div>

        <div
          class="nav-section protected-section bsn-protected"
          style="display: none"
        >
          <h3>BSN Sports Integration</h3>
          <ul>
            <li>
              <a href="#bsn-error-handling" class="nav-link"
                >BSN Error Handling</a
              >
            </li>
            <li>
              <a href="#bsn-endpoints" class="nav-link">Purchase Order</a>
              <ul class="sub-nav">
                <li>
                  <a href="#bsn-po-submit" class="nav-link"
                    >Submit Purchase Order</a
                  >
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="content">
        <!-- Floating Header -->
        <div class="floating-header">
          <div class="header-content">
            <div class="header-title">
              <h2>API Documentation</h2>
            </div>
            <div class="header-controls">
              <div class="theme-selector">
                <button
                  onclick="toggleTheme()"
                  class="theme-toggle"
                  id="themeToggle"
                >
                  <span class="theme-icon">🌙</span>
                </button>
              </div>
              <div class="header-login">
                <div class="login-form">
                  <input
                    type="password"
                    id="sectionPassword"
                    placeholder="Enter section password..."
                    class="password-input"
                  />
                  <button onclick="unlockSections()" class="unlock-button">
                    Unlock
                  </button>
                </div>
                <div class="login-status" id="loginStatus"></div>
              </div>
            </div>
          </div>
        </div>
        <!-- Introduction Section -->
        <section id="introduction" class="section">
          <div class="section-content">
            <div class="text-column">
              <h1>Introduction</h1>
              <p>
                The Varpro Bifrost API is a RESTful API for managing orders in
                the Varpro ecosystem. It provides endpoints for order management
                and BSN Sports integration.
              </p>

              <h2>Base URL</h2>
              <p>All API requests should be made to:</p>
              <code class="inline-code">https://bifrost.varpro.org</code>

              <h2>Content Type</h2>
              <p>
                All requests and responses use JSON format. Make sure to include
                the appropriate Content-Type header:
              </p>
              <code class="inline-code">Content-Type: application/json</code>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Base Endpoint</span>
                </div>
                <pre><code class="language-bash">https://bifrost.varpro.org</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- Authentication Section -->
        <section id="authentication" class="section">
          <div class="section-content">
            <div class="text-column">
              <h1>Authentication</h1>
              <p>
                The Varpro Bifrost API uses API key authentication. You must
                include your API key in the request header for all API calls.
              </p>

              <h2>API Key Header</h2>
              <p>
                Include your API key in the
                <code class="inline-code">x-varpro-company-token</code> header:
              </p>

              <h2>Getting Your API Key</h2>
              <p>
                Contact your Varpro administrator to obtain your API key. Each
                customer has a unique API key that identifies your organization.
              </p>

              <h2>Security</h2>
              <p>
                Keep your API key secure and never expose it in client-side
                code. All API requests must be made over HTTPS.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Authentication Header</span>
                </div>
                <pre><code class="language-bash">curl -X GET \
  https://bifrost.varpro.org/api \
  -H 'x-varpro-company-token: your-api-key-here'</code></pre>
              </div>

              <div class="code-example">
                <div class="code-header">
                  <span>Unauthorized Response</span>
                </div>
                <pre><code class="language-json">{
  "error": "Unauthorized"
}</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- Error Handling Section -->
        <section id="errors" class="section">
          <div class="section-content">
            <div class="text-column">
              <h1>Error Handling</h1>
              <p>
                The API uses conventional HTTP response codes to indicate the
                success or failure of an API request.
              </p>

              <h2>HTTP Status Codes</h2>
              <ul>
                <li><strong>200</strong> - OK: The request was successful</li>
                <li>
                  <strong>201</strong> - Created: The resource was successfully
                  created
                </li>
                <li>
                  <strong>400</strong> - Bad Request: Invalid request parameters
                </li>
                <li>
                  <strong>401</strong> - Unauthorized: Invalid or missing API
                  key
                </li>
                <li>
                  <strong>500</strong> - Internal Server Error: Server error
                </li>
              </ul>

              <h2>Error Response Format</h2>
              <p>
                Error responses include a descriptive error message and may
                include additional details for validation errors.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Validation Error (400)</span>
                </div>
                <pre><code class="language-json">{
  "error": "Invalid data",
  "details": [
    {
      "message": "purchaseOrder is Required"
    },
    {
      "message": "quantity must be a positive number"
    }
  ]
}</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- Order Endpoints Section -->
        <section id="order-endpoints" class="section">
          <div class="section-content">
            <div class="text-column">
              <h1>Order Endpoints</h1>
              <p>
                Order endpoints provide access to order management
                functionality, including retrieving order details from the Poly
                system.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>ENDPOINTS</span>
                </div>
                <div class="endpoint-list">
                  <div class="endpoint-item">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/order/details</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Order Response Schema -->
        <section id="order-response-schema" class="section">
          <div class="section-content">
            <div class="text-column">
              <h2>Order Response Schema</h2>
              <p>
                Order endpoints return an array of order detail objects from the
                Poly system, with each object representing a single order item
                with comprehensive tracking information.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Order Detail Object Schema</span>
                </div>
                <pre><code class="language-json">{
  "CustomerNumber": "12345",
  "OrderStatus": "Committed",
  "RetailerPONumber": "PO-2025-001",
  "PONumber": "PO123ABC",
  "RequiredDate": "2025-02-15",
  "OrderNumber": "123456",
  "ItemNumber": "12345",
  "StyleNumber": "ST-12345",
  "StyleColor": "Navy Blue",
  "UnitCost": "25.99",
  "GarmentSize": "Large",
  "ItemDescription8_": "JER123A4",
  "ActualCount": "2",
  "Description": "SMITH, JOHNSON",
  "DetailSpec2": "23, 45",
  "AllocateCount": 2,
  "QuantityFinished": 0,
  "PackCount": 0,
  "ShipCount": 0
}</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- GET /api/v1/order/details -->
        <section id="order-details" class="endpoint-section">
          <div class="section-content">
            <div class="text-column">
              <div class="endpoint-header">
                <span class="method get">GET</span>
                <span class="endpoint-path">/api/v1/order/details</span>
              </div>
              <p>
                Retrieves active order details for the authenticated customer
                from the Poly system. Returns orders with status: Committed,
                New, Pending, Processed, Shipped, or Released.
              </p>

              <h3>Response</h3>
              <p>
                Returns an array of order detail objects containing
                comprehensive information about each order item, including
                customer details, order status, product specifications,
                quantities, and fulfillment progress.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Request</span>
                </div>
                <pre><code class="language-bash">curl -X GET \
  https://bifrost.varpro.org/api/v1/order/details \
  -H 'x-varpro-company-token: your-api-key'</code></pre>
              </div>

              <div class="code-example">
                <div class="code-header">
                  <span>Response</span>
                </div>
                <pre><code class="language-json">{
  "data": [
    {
      "CustomerNumber": "12345",
      "OrderStatus": "Committed",
      "RetailerPONumber": "PO-2025-001",
      "PONumber": "PO123ABC",
      "RequiredDate": "2025-02-15",
      "OrderNumber": "123456",
      "ItemNumber": "12345",
      "StyleNumber": "ST-12345",
      "StyleColor": "Navy Blue",
      "UnitCost": "25.99",
      "GarmentSize": "Large",
      "ItemDescription8_": "JER123A4",
      "ActualCount": "2",
      "Description": "SMITH, JOHNSON",
      "DetailSpec2": "23, 45",
      "AllocateCount": 2,
      "QuantityFinished": 0,
      "PackCount": 0,
      "ShipCount": 0
    },
    {
      "CustomerNumber": "12345",
      "OrderStatus": "Processed",
      "RetailerPONumber": "PO-2025-002",
      "PONumber": "PO456DEF78",
      "RequiredDate": "2025-02-20",
      "OrderNumber": "789012",
      "ItemNumber": "67890",
      "StyleNumber": "ST-67890",
      "StyleColor": "Red",
      "UnitCost": "18.50",
      "GarmentSize": "Medium",
      "ItemDescription8_": "SHT789B2",
      "ActualCount": "2",
      "Description": "WILLIAMS, DAVIS",
      "DetailSpec2": "12, 88",
      "AllocateCount": 2,
      "QuantityFinished": 2,
      "PackCount": 2,
      "ShipCount": 0
    }
  ]
}</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- BSN Error Handling Section -->
        <section
          id="bsn-error-handling"
          class="section protected-content bsn-protected"
          style="display: none"
        >
          <div class="section-content">
            <div class="text-column">
              <h1>BSN Error Handling</h1>
              <p>
                BSN endpoints use a specific error response format that includes
                transaction IDs for tracking and debugging purposes.
              </p>

              <h2>BSN Error Response Format</h2>
              <p>
                All BSN endpoints return errors in a standardized format with
                status, data, and message fields. The data field always includes
                a transaction ID for support purposes.
              </p>

              <h2>Common BSN Error Scenarios</h2>
              <ul>
                <li>
                  <strong>Unauthorized Access:</strong> Customer not authorized
                  for BSN endpoints
                </li>
                <li>
                  <strong>Validation Errors:</strong> Invalid purchase order
                  data
                </li>
                <li>
                  <strong>Processing Errors:</strong> Issues with BSN system
                  integration
                </li>
              </ul>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>BSN Error Response</span>
                </div>
                <pre><code class="language-json">{
  "status": "error",
  "data": {
    "transactionId": "12345678-1234-1234-1234-123456789012"
  },
  "message": "Bad Request: Validation error details"
}</code></pre>
              </div>

              <div class="code-example">
                <div class="code-header">
                  <span>BSN Unauthorized Response</span>
                </div>
                <pre><code class="language-json">{
  "status": "error",
  "data": {
    "transactionId": "12345678-1234-1234-1234-123456789012"
  },
  "message": "Unauthorized: Customer not authorized"
}</code></pre>
              </div>
            </div>
          </div>
        </section>

        <!-- BSN Endpoints Section -->
        <section
          id="bsn-endpoints"
          class="section protected-content bsn-protected"
          style="display: none"
        >
          <div class="section-content">
            <div class="text-column">
              <h1>Purchase Order</h1>
              <p>
                Purchase order endpoints enable submission and management of
                orders through the BSN Sports system. These endpoints handle
                order validation, processing, and status tracking for custom
                apparel and sporting goods.
              </p>
              <p>
                <strong>Note:</strong> Purchase order endpoints are restricted
                to authorized BSN customers only (Customer ID 1).
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>ENDPOINTS</span>
                </div>
                <div class="endpoint-list">
                  <div class="endpoint-item">
                    <span class="method post">POST</span>
                    <span class="endpoint-url">/api/v1/bsn/po/submit</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- POST /api/v1/bsn/po/submit -->
        <section
          id="bsn-po-submit"
          class="endpoint-section protected-content bsn-protected"
          style="display: none"
        >
          <div class="section-content">
            <div class="text-column">
              <div class="endpoint-header">
                <span class="method post">POST</span>
                <span class="endpoint-path">/api/v1/bsn/po/submit</span>
              </div>
              <p>
                Submit purchase orders to the BSN Sports system. This endpoint
                validates the purchase order data and creates CSV files for BSN
                processing.
              </p>

              <h3>Request Body Attributes</h3>

              <h4>Root Object</h4>
              <div class="child-attributes-section">
                <div class="child-attributes-header">
                  <div>
                    <span class="property-name">purchaseOrders</span>
                    <span class="property-type">(array, required)</span><br />
                    <span class="property-description"
                      >Array of purchase order objects to be submitted to BSN
                      Sports system.</span
                    >
                  </div>
                  <button class="child-attributes-toggle">
                    <span class="toggle-text">Show child attributes</span>
                    <span class="toggle-icon">+</span>
                  </button>
                </div>
                <div class="child-attributes-content">
                  <div class="property-block">
                    <p>
                      <span class="property-name">purchaseOrder</span>
                      <span class="property-type">string</span>
                      <span class="property-required">required</span><br />
                      <span class="property-description"
                        >Unique purchase order number identifying this
                        order.</span
                      >
                    </p>
                  </div>

                  <div class="property-block">
                    <p>
                      <span class="property-name">orderType</span>
                      <span class="property-type">enum</span>
                      <span class="property-required">required</span><br />
                      <span class="property-description"
                        >Type of order. Must be one of: "MTS", "STH", or
                        "REG".</span
                      >
                    </p>
                  </div>

                  <div class="property-block">
                    <p>
                      <span class="property-name">batchId</span>
                      <span class="property-type">string</span><br />
                      <span class="property-description"
                        >Batch identifier for grouping related orders
                        together.</span
                      >
                    </p>
                  </div>

                  <div class="property-block">
                    <p>
                      <span class="property-name">refTransactionNumber</span>
                      <span class="property-type">string</span>
                      <span class="property-required">required</span><br />
                      <span class="property-description"
                        >Reference transaction number from the originating
                        system.</span
                      >
                    </p>
                  </div>

                  <div class="property-block">
                    <p>
                      <span class="property-name">refTransactionDate</span>
                      <span class="property-type">string</span>
                      <span class="property-required">required</span><br />
                      <span class="property-description"
                        >Date of the reference transaction in YYYY-MM-DD
                        format.</span
                      >
                    </p>
                  </div>

                  <div class="child-attributes-section">
                    <div class="child-attributes-header">
                      <div>
                        <span class="property-name">items</span>
                        <span class="property-type">array</span>
                        <span class="property-required">required</span><br />
                        <span class="property-description"
                          >Array of order item objects containing product and
                          shipping details.</span
                        >
                      </div>
                      <button class="child-attributes-toggle">
                        <span class="toggle-text">Show child attributes</span>
                        <span class="toggle-icon">+</span>
                      </button>
                    </div>
                    <div class="child-attributes-content">
                      <div class="property-block">
                        <p>
                          <span class="property-name">itemLineNumber</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Line number for this item within the order.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">productCode</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >BSN product code/SKU for the item being
                            ordered.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">productDescription</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Human-readable description of the product.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name"
                            >customerPurchaseOrder</span
                          >
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Customer's purchase order number if different from
                            main PO.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">salesOrder</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Sales order number from the originating
                            system.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">designId</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Unique identifier for the design/artwork to be
                            applied.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">quantity</span>
                          <span class="property-type">number</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Number of units to be ordered for this item.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">uom</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Unit of measure. Defaults to "EA" (each) if not
                            specified.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">unitCost</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Cost per unit in decimal format (e.g.,
                            "25.99").</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">itemTotal</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Total cost for this line item (quantity ×
                            unitCost).</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">currency</span>
                          <span class="property-type">enum</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Currency code. Must be "USD".</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">productType</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Type or category of the product being
                            ordered.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">productColor</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Color specification for the product.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">productSize</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Size specification for the product.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">rosterName</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Name to be printed on the product (for personalized
                            items).</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">personalizationName</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Additional personalization name field.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name"
                            >personalizationNumber</span
                          >
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Number to be printed on the product (jersey
                            numbers, etc.).</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">company</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Company name associated with this order item.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingAttention</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Attention line for shipping address.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingAddress1</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Primary shipping address line.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingAddress2</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Secondary shipping address line (apartment, suite,
                            etc.).</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingCity</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Shipping city name.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingState</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Shipping state or province code.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingZip</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Shipping postal/ZIP code.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingCountry</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Shipping country code.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingTel</span>
                          <span class="property-type">string</span>
                          <span class="property-required">required</span><br />
                          <span class="property-description"
                            >Shipping contact telephone number.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">shippingEmail</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >Shipping contact email address.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">printFileUrl</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >URL to the print-ready file for this item.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">printFileZipUrl</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >URL to a ZIP file containing print-ready
                            files.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">artworkPreviewUrl</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >URL to a preview image of the artwork.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">parentDefaultImage</span>
                          <span class="property-type">string</span><br />
                          <span class="property-description"
                            >URL to the default product image.</span
                          >
                        </p>
                      </div>

                      <div class="property-block">
                        <p>
                          <span class="property-name">parentFilmStripUrls</span>
                          <span class="property-type">array</span><br />
                          <span class="property-description"
                            >Array of objects containing parentFilmStripUrl
                            strings for product images.</span
                          >
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <h3>Response</h3>
              <p>
                Returns a success response with transaction details or error
                information.
              </p>
            </div>
            <div class="code-column">
              <div class="code-example">
                <div class="code-header">
                  <span>Request</span>
                </div>
                <pre><code class="language-bash">curl -X POST \
  https://bifrost.varpro.org/api/v1/bsn/po/submit \
  -H 'x-varpro-company-token: your-api-key' \
  -H 'Content-Type: application/json' \
  -d @purchase_order.json</code></pre>
              </div>

              <div class="code-example">
                <div class="code-header">
                  <span>Request Body Example</span>
                </div>
                <pre><code class="language-json">{
  "purchaseOrders": [
    {
      "purchaseOrder": "PO-12345",
      "orderType": "REG",
      "batchId": "BATCH-001",
      "refTransactionNumber": "TXN-67890",
      "refTransactionDate": "2025-01-22",
      "items": [
        {
          "itemLineNumber": "00010",
          "productCode": "BSN70504ZOS",
          "productDescription": "Custom Team Jersey",
          "customerPurchaseOrder": "CUST-PO-456",
          "salesOrder": "SO-12345",
          "designId": "249ae809-3cad-49a1-947a-b548ced571c6",
          "quantity": 35,
          "uom": "EA",
          "unitCost": "25.99",
          "itemTotal": "909.65",
          "currency": "USD",
          "productType": "Jersey",
          "productColor": "Navy Blue",
          "productSize": "Large",
          "rosterName": "Smith",
          "personalizationNumber": "23",
          "company": "Eagles Sports Club",
          "shippingAttention": "Equipment Manager",
          "shippingAddress1": "123 Main St",
          "shippingAddress2": "Suite 100",
          "shippingCity": "Chicago",
          "shippingState": "IL",
          "shippingZip": "60060",
          "shippingCountry": "US",
          "shippingTel": "************",
          "shippingEmail": "<EMAIL>",
          "printFileUrl": "https://example.com/print/design123.pdf",
          "artworkPreviewUrl": "https://example.com/preview/design123.jpg"
        }
      ]
    }
  ]
}</code></pre>
              </div>

              <div class="code-example">
                <div class="code-header">
                  <span>Success Response</span>
                </div>
                <pre><code class="language-json">{
  "status": "success",
  "data": {
    "transactionId": "12345678-1234-1234-1234-123456789012"
  },
  "message": "Purchase orders processed successfully"
}</code></pre>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
  </body>
</html>
