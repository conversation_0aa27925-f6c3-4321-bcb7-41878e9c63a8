import React, { useState, useCallback } from 'react'
import { useSearchMos, useAvailableMos } from '@hooks/useMos'
import { useDebounce } from '@hooks/useDebounce'
import { DataTable, type Column } from '@components/DataTable/DataTable'
import { Pagination } from '@components/Pagination/Pagination'
import { Button } from '@components/Button/Button'
import { Input } from '@components/Input/Input'
import { Label } from '@components/Label/Label'
import { FormGroup } from '@components/FormGroup/FormGroup'

interface MoSelectionProps {
  onSelect: (moId: number) => void
}

interface Mo {
  mo_id: number
  mo_order: string
  customer: string
  num: string
  created_at: string
}

export const MoSelection: React.FC<MoSelectionProps> = ({ onSelect }) => {
  const [searchParams, setSearchParams] = useState({
    mo_order: '',
    customer: '',
    num: '',
  })
  const [page, setPage] = useState(1)
  const limit = 10

  // Use the debounce hook
  const [debouncedSearchParams, isDebouncing] = useDebounce(searchParams, 300)

  // Define table columns
  const columns: Column<Mo>[] = [
    {
      key: 'action',
      header: 'Action',
      className: 'action-column',
      render: (_, row) => (
        <Button
          onClick={() => onSelect(row.mo_id)}
          variant="success"
          size="sm"
        >
          Select
        </Button>
      )
    },
    {
      key: 'mo_order',
      header: 'MO Order'
    },
    {
      key: 'customer',
      header: 'Customer'
    },
    {
      key: 'num',
      header: 'Number'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  const hasSearchParams = debouncedSearchParams.mo_order || debouncedSearchParams.customer || debouncedSearchParams.num

  const { data: searchResults, isLoading: isSearching } = useSearchMos({
    ...debouncedSearchParams,
    page,
    limit,
  })

  const { data: availableMos, isLoading: isLoadingAvailable } = useAvailableMos(
    limit,
    (page - 1) * limit
  )

  const data = hasSearchParams ? searchResults : availableMos
  const isLoading = hasSearchParams ? isSearching : isLoadingAvailable

  const handleInputChange = useCallback((field: string, value: string) => {
    setSearchParams(prev => ({ ...prev, [field]: value }))
  }, [])

  const clearSearch = useCallback(() => {
    setSearchParams({ mo_order: '', customer: '', num: '' })
    setPage(1)
  }, [])

  const mos = data?.data || []
  const total = data?.total || 0
  const totalPages = data?.totalPages || Math.ceil(total / limit)
  const currentPage = data?.page || page
  const currentLimit = data?.limit || limit

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Search Manufacturing Orders</h2>
        {isDebouncing && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
            <span className="text-blue-700 font-medium">Searching...</span>
          </div>
        )}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormGroup>
              <Label htmlFor="mo_order">MO Order</Label>
              <Input
                type="text"
                id="mo_order"
                value={searchParams.mo_order}
                onChange={(e) => handleInputChange('mo_order', e.target.value)}
                placeholder="Enter MO order number"
              />
            </FormGroup>
            <FormGroup>
              <Label htmlFor="customer">Customer</Label>
              <Input
                type="text"
                id="customer"
                value={searchParams.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
                placeholder="Enter customer name"
              />
            </FormGroup>
            <FormGroup>
              <Label htmlFor="num">Number</Label>
              <Input
                type="text"
                id="num"
                value={searchParams.num}
                onChange={(e) => handleInputChange('num', e.target.value)}
                placeholder="Enter number"
              />
            </FormGroup>
          </div>
          {(searchParams.mo_order || searchParams.customer || searchParams.num) && (
            <div className="flex justify-start">
              <Button
                type="button"
                onClick={clearSearch}
                variant="secondary"
              >
                Clear Search
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {hasSearchParams ? 'Search Results' : 'Available Manufacturing Orders'}
          {total > 0 && (
            <span className="text-sm font-normal text-gray-600 ml-2">
              ({total} total)
            </span>
          )}
        </h3>

        <DataTable
          data={mos}
          columns={columns}
          keyField="mo_id"
          minWidth="500px"
          loading={isLoading}
          loadingMessage="Loading manufacturing orders..."
          emptyMessage={
            hasSearchParams
              ? 'No manufacturing orders found matching your search criteria.'
              : 'No available manufacturing orders found.'
          }
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setPage}
          total={total}
          limit={currentLimit}
        />
      </div>
    </div>
  )
}


