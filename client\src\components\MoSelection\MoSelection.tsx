import React, { useState, useEffect, useCallback } from 'react'
import { useSearchMos, useAvailableMos } from '@hooks/useMos'
import { DataTable, type Column } from '@components/DataTable/DataTable'
import { Pagination } from '@components/Pagination/Pagination'

interface MoSelectionProps {
  onSelect: (moId: number) => void
}

interface Mo {
  mo_id: number
  mo_order: string
  customer: string
  num: string
  created_at: string
}

export const MoSelection: React.FC<MoSelectionProps> = ({ onSelect }) => {
  const [searchParams, setSearchParams] = useState({
    mo_order: '',
    customer: '',
    num: '',
  })
  const [debouncedSearchParams, setDebouncedSearchParams] = useState({
    mo_order: '',
    customer: '',
    num: '',
  })
  const [page, setPage] = useState(1)
  const limit = 10

  // Define table columns
  const columns: Column<Mo>[] = [
    {
      key: 'action',
      header: 'Action',
      className: 'action-column',
      render: (_, row) => (
        <button
          onClick={() => onSelect(row.mo_id)}
          className="btn-action bg-success-500 hover:bg-success-600 text-white"
        >
          Select
        </button>
      )
    },
    {
      key: 'mo_order',
      header: 'MO Order'
    },
    {
      key: 'customer',
      header: 'Customer'
    },
    {
      key: 'num',
      header: 'Number'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  // Debounce search params to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchParams(searchParams)
      setPage(1) // Reset to first page when search changes
    }, 300) // 300ms delay

    return () => clearTimeout(timer)
  }, [searchParams])

  const hasSearchParams = debouncedSearchParams.mo_order || debouncedSearchParams.customer || debouncedSearchParams.num

  const { data: searchResults, isLoading: isSearching } = useSearchMos({
    ...debouncedSearchParams,
    page,
    limit,
  })

  const { data: availableMos, isLoading: isLoadingAvailable } = useAvailableMos(
    limit,
    (page - 1) * limit
  )

  const data = hasSearchParams ? searchResults : availableMos
  const isLoading = hasSearchParams ? isSearching : isLoadingAvailable

  // Check if search is being debounced
  const isDebouncing = searchParams.mo_order !== debouncedSearchParams.mo_order ||
    searchParams.customer !== debouncedSearchParams.customer ||
    searchParams.num !== debouncedSearchParams.num

  const handleInputChange = useCallback((field: string, value: string) => {
    setSearchParams(prev => ({ ...prev, [field]: value }))
  }, [])

  const clearSearch = useCallback(() => {
    setSearchParams({ mo_order: '', customer: '', num: '' })
    setDebouncedSearchParams({ mo_order: '', customer: '', num: '' })
    setPage(1)
  }, [])

  const mos = data?.data || []
  const total = data?.total || 0
  const totalPages = data?.totalPages || Math.ceil(total / limit)
  const currentPage = data?.page || page
  const currentLimit = data?.limit || limit

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Search Manufacturing Orders</h2>
        {isDebouncing && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
            <span className="text-blue-700 font-medium">Searching...</span>
          </div>
        )}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <label htmlFor="mo_order" className="form-label">MO Order</label>
              <input
                type="text"
                id="mo_order"
                value={searchParams.mo_order}
                onChange={(e) => handleInputChange('mo_order', e.target.value)}
                placeholder="Enter MO order number"
                className="form-input"
              />
            </div>
            <div className="space-y-1">
              <label htmlFor="customer" className="form-label">Customer</label>
              <input
                type="text"
                id="customer"
                value={searchParams.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
                placeholder="Enter customer name"
                className="form-input"
              />
            </div>
            <div className="space-y-1">
              <label htmlFor="num" className="form-label">Number</label>
              <input
                type="text"
                id="num"
                value={searchParams.num}
                onChange={(e) => handleInputChange('num', e.target.value)}
                placeholder="Enter number"
                className="form-input"
              />
            </div>
          </div>
          {(searchParams.mo_order || searchParams.customer || searchParams.num) && (
            <div className="flex justify-start">
              <button
                type="button"
                onClick={clearSearch}
                className="btn-secondary"
              >
                Clear Search
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {hasSearchParams ? 'Search Results' : 'Available Manufacturing Orders'}
          {total > 0 && (
            <span className="text-sm font-normal text-gray-600 ml-2">
              ({total} total)
            </span>
          )}
        </h3>

        <DataTable
          data={mos}
          columns={columns}
          keyField="mo_id"
          minWidth="500px"
          loading={isLoading}
          loadingMessage="Loading manufacturing orders..."
          emptyMessage={
            hasSearchParams
              ? 'No manufacturing orders found matching your search criteria.'
              : 'No available manufacturing orders found.'
          }
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setPage}
          total={total}
          limit={currentLimit}
        />
      </div>
    </div>
  )
}


