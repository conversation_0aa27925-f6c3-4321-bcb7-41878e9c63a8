import React, { useState, useEffect, useCallback } from 'react'
import { useSearchMos, useAvailableMos } from '../../hooks/useMos'
import DataTable, { type Column } from '../DataTable/DataTable'
import Pagination from '../Pagination/Pagination'
import './MoSelection.css'

interface MoSelectionProps {
  onSelect: (moId: number) => void
}

interface Mo {
  mo_id: number
  mo_order: string
  customer: string
  num: string
  created_at: string
}

const MoSelection: React.FC<MoSelectionProps> = ({ onSelect }) => {
  const [searchParams, setSearchParams] = useState({
    mo_order: '',
    customer: '',
    num: '',
  })
  const [debouncedSearchParams, setDebouncedSearchParams] = useState({
    mo_order: '',
    customer: '',
    num: '',
  })
  const [page, setPage] = useState(1)
  const limit = 10

  // Define table columns
  const columns: Column<Mo>[] = [
    {
      key: 'action',
      header: 'Action',
      className: 'action-column',
      render: (_, row) => (
        <button
          onClick={() => onSelect(row.mo_id)}
          className="btn-action btn-select"
        >
          Select
        </button>
      )
    },
    {
      key: 'mo_order',
      header: 'MO Order'
    },
    {
      key: 'customer',
      header: 'Customer'
    },
    {
      key: 'num',
      header: 'Number'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  // Debounce search params to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchParams(searchParams)
      setPage(1) // Reset to first page when search changes
    }, 300) // 300ms delay

    return () => clearTimeout(timer)
  }, [searchParams])

  const hasSearchParams = debouncedSearchParams.mo_order || debouncedSearchParams.customer || debouncedSearchParams.num

  const { data: searchResults, isLoading: isSearching } = useSearchMos({
    ...debouncedSearchParams,
    page,
    limit,
  })

  const { data: availableMos, isLoading: isLoadingAvailable } = useAvailableMos(
    limit,
    (page - 1) * limit
  )

  const data = hasSearchParams ? searchResults : availableMos
  const isLoading = hasSearchParams ? isSearching : isLoadingAvailable

  // Check if search is being debounced
  const isDebouncing = searchParams.mo_order !== debouncedSearchParams.mo_order ||
    searchParams.customer !== debouncedSearchParams.customer ||
    searchParams.num !== debouncedSearchParams.num

  const handleInputChange = useCallback((field: string, value: string) => {
    setSearchParams(prev => ({ ...prev, [field]: value }))
  }, [])

  const clearSearch = useCallback(() => {
    setSearchParams({ mo_order: '', customer: '', num: '' })
    setDebouncedSearchParams({ mo_order: '', customer: '', num: '' })
    setPage(1)
  }, [])

  const mos = data?.data || []
  const total = data?.total || 0
  const totalPages = data?.totalPages || Math.ceil(total / limit)
  const currentPage = data?.page || page
  const currentLimit = data?.limit || limit

  return (
    <div className="mo-selection">
      <div className="search-section">
        <h2>Search Manufacturing Orders</h2>
        {isDebouncing && (
          <div className="search-indicator">
            <span>Searching...</span>
          </div>
        )}
        <div className="search-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="mo_order">MO Order</label>
              <input
                type="text"
                id="mo_order"
                value={searchParams.mo_order}
                onChange={(e) => handleInputChange('mo_order', e.target.value)}
                placeholder="Enter MO order number"
              />
            </div>
            <div className="form-group">
              <label htmlFor="customer">Customer</label>
              <input
                type="text"
                id="customer"
                value={searchParams.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
                placeholder="Enter customer name"
              />
            </div>
            <div className="form-group">
              <label htmlFor="num">Number</label>
              <input
                type="text"
                id="num"
                value={searchParams.num}
                onChange={(e) => handleInputChange('num', e.target.value)}
                placeholder="Enter number"
              />
            </div>
          </div>
          {(searchParams.mo_order || searchParams.customer || searchParams.num) && (
            <div className="form-actions">
              <button type="button" onClick={clearSearch} className="btn-clear">
                Clear Search
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="results-section">
        <h3>
          {hasSearchParams ? 'Search Results' : 'Available Manufacturing Orders'}
          {total > 0 && ` (${total} total)`}
        </h3>

        <DataTable
          data={mos}
          columns={columns}
          keyField="mo_id"
          minWidth="500px"
          loading={isLoading}
          loadingMessage="Loading manufacturing orders..."
          emptyMessage={
            hasSearchParams
              ? 'No manufacturing orders found matching your search criteria.'
              : 'No available manufacturing orders found.'
          }
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setPage}
          total={total}
          limit={currentLimit}
        />
      </div>
    </div>
  )
}

export default MoSelection
