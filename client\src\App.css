@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-success {
    @apply bg-success-500 hover:bg-success-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-danger {
    @apply bg-danger-500 hover:bg-danger-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-action {
    @apply py-1.5 px-3 text-sm font-medium rounded transition-all duration-300 hover:-translate-y-0.5;
  }

  .card {
    @apply bg-white rounded-xl shadow-card p-6;
  }

  .card-hover {
    @apply hover:shadow-card-hover transition-shadow duration-300;
  }

  .form-input {
    @apply w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:border-primary-500 focus:outline-none transition-colors duration-300;
  }

  .form-label {
    @apply block text-sm font-semibold text-gray-700 mb-1;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-yes {
    @apply bg-success-100 text-success-800;
  }

  .badge-no {
    @apply bg-gray-100 text-gray-800;
  }
}
