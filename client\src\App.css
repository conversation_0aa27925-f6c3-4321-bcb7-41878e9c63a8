@import "tailwindcss";

/* Custom theme configuration */
@theme {
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #667eea;
  --color-primary-600: #5a6fd8;
  --color-primary-700: #4c63d2;
  --color-primary-800: #4338ca;
  --color-primary-900: #3730a3;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;

  --shadow-card: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-card-hover: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Custom component classes */
.btn-primary {
  @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
}

.btn-success {
  @apply bg-success-500 hover:bg-success-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
}

.btn-danger {
  @apply bg-danger-500 hover:bg-danger-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
}

.btn-action {
  @apply py-1.5 px-3 text-sm font-medium rounded transition-all duration-300 hover:-translate-y-0.5;
}

.card {
  @apply bg-white rounded-xl p-6;
  box-shadow: var(--shadow-card);
}

.card-hover {
  @apply transition-shadow duration-300;
}

.card-hover:hover {
  box-shadow: var(--shadow-card-hover);
}

.form-input {
  @apply w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:border-primary-500 focus:outline-none transition-colors duration-300;
}

.form-label {
  @apply block text-sm font-semibold text-gray-700 mb-1;
}

.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-yes {
  @apply bg-success-100 text-success-800;
}

.badge-no {
  @apply bg-gray-100 text-gray-800;
}
