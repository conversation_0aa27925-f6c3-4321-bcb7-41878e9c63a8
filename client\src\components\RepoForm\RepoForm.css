/* Repo Form Styles */
.repo-form {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mo-info {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mo-info h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.mo-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.detail-item {
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.detail-item strong {
  color: #333;
  display: block;
  margin-bottom: 0.2rem;
  font-size: 0.9rem;
}

.btn-back {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1.25rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-back:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.form {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: #333;
  font-weight: 600;
  margin-bottom: 0.4rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.6rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input[type='checkbox'] {
  width: auto;
  margin-right: 0.5rem;
}

.work-areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.work-area-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.work-area-item:hover {
  background: #e9ecef;
}

.work-area-item input[type='checkbox'] {
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

.btn-cancel,
.btn-submit {
  padding: 0.5rem 1.75rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-submit {
  background: #667eea;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading,
.error {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error h3 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.error p {
  color: #666;
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .repo-form {
    margin: 0 1rem;
  }

  .mo-details {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .work-areas-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

@media (max-width: 768px) {
  .repo-form {
    margin: 0 0.5rem;
    gap: 1rem;
  }

  .mo-info,
  .form {
    padding: 1rem;
    border-radius: 8px;
  }

  .mo-info h2,
  .form h2 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .mo-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .detail-item {
    padding: 0.6rem;
  }

  .detail-item strong {
    font-size: 0.85rem;
  }

  .btn-back {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.75rem;
    font-size: 1rem;
  }

  .form-group textarea {
    min-height: 80px;
  }

  .work-areas-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .work-area-item {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
    padding: 0.625rem 1rem;
    font-size: 1rem;
  }

  .loading,
  .error {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .repo-form {
    margin: 0 0.25rem;
  }

  .mo-info,
  .form {
    padding: 0.75rem;
    border-radius: 6px;
  }

  .mo-info h2,
  .form h2 {
    font-size: 1.1rem;
  }

  .detail-item {
    padding: 0.5rem;
  }

  .detail-item strong {
    font-size: 0.8rem;
  }

  .btn-back {
    padding: 0.375rem 0.875rem;
    font-size: 0.85rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.875rem;
  }

  .work-area-item {
    padding: 0.6rem;
    font-size: 0.85rem;
  }

  .btn-cancel,
  .btn-submit {
    padding: 0.75rem;
  }
}

@media (max-width: 360px) {
  .repo-form {
    margin: 0 0.125rem;
  }

  .mo-info,
  .form {
    padding: 0.5rem;
  }

  .mo-info h2,
  .form h2 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .form-group label {
    font-size: 0.85rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 0.9rem;
  }
}
