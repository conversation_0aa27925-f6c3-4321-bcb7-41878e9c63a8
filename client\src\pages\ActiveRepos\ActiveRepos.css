/* Active Repos Page Styles */
.active-repos {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #333;
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.repos-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.repos-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 700px;
}

.repos-table th {
  background: #f8f9fa;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  font-size: 0.9rem;
}

.repos-table th:first-child {
  width: 100px;
  text-align: center;
}

.repos-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  color: #555;
  font-size: 0.9rem;
}

.repos-table td:first-child {
  text-align: center;
  padding: 0.5rem;
}

.repos-table tr:hover {
  background: #f8f9fa;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.yes {
  background: #dc3545;
  color: white;
}

.badge.no {
  background: #28a745;
  color: white;
}

.btn-view {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  min-width: 60px;
  line-height: 1.2;
}

.btn-view:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-page {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 0.375rem 0.875rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.btn-page:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-weight: 500;
}

.pagination-info {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.empty-state h3 {
  color: #333;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #666;
}

.loading,
.error {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error {
  color: #dc3545;
}

.error h2 {
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .active-repos {
    margin: 0 1rem;
  }
}

@media (max-width: 768px) {
  .active-repos {
    margin: 0 0.5rem;
  }

  .repos-table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
  }

  .repos-table {
    min-width: 600px;
  }

  .repos-table th,
  .repos-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }

  .repos-table th:first-child,
  .repos-table td:first-child {
    width: 80px;
    padding: 0.25rem;
  }

  .repos-table th:last-child,
  .repos-table td:last-child {
    padding-right: 0.5rem;
  }

  .btn-view {
    padding: 0.375rem 0.625rem;
    font-size: 0.85rem;
  }

  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .pagination-nav {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .pagination .page-info {
    text-align: center;
    order: -1;
  }

  .header h1 {
    font-size: 1.6rem;
  }

  .header p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .active-repos {
    margin: 0 0.25rem;
  }

  .repos-table {
    min-width: 500px;
  }

  .repos-table th,
  .repos-table td {
    padding: 0.4rem 0.2rem;
    font-size: 0.8rem;
  }

  .repos-table th:first-child,
  .repos-table td:first-child {
    width: 70px;
    padding: 0.2rem;
  }

  .btn-view {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .header h1 {
    font-size: 1.4rem;
  }

  .header p {
    font-size: 0.85rem;
  }
}
