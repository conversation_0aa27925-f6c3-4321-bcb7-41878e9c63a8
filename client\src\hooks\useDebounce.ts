import { useState, useEffect } from 'react'

/**
 * Custom hook that debounces a value
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value and a boolean indicating if debouncing is in progress
 */
export function useDebounce<T>(value: T, delay: number): [T, boolean] {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const [isDebouncing, setIsDebouncing] = useState(false)

  useEffect(() => {
    // Set debouncing to true when value changes
    setIsDebouncing(true)
    
    // Set up the timeout
    const handler = setTimeout(() => {
      setDebouncedValue(value)
      setIsDebouncing(false)
    }, delay)

    // Cleanup function to clear timeout
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  // Initialize debounced value on first render
  useEffect(() => {
    setDebouncedValue(value)
    setIsDebouncing(false)
  }, [])

  return [debouncedValue, isDebouncing]
}
