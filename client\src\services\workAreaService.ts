import { apiClient } from '../lib/api'
import type { WorkArea } from '../types'

export const workAreaService = {
  // Get active work areas
  async getActiveWorkAreas(): Promise<WorkArea[]> {
    // This endpoint needs to be created on the server
    const response = await apiClient.get('/v1/work-areas/active')
    return response.data
  },

  // Get all work areas
  async getAllWorkAreas(): Promise<WorkArea[]> {
    // This endpoint needs to be created on the server
    const response = await apiClient.get('/v1/work-areas')
    return response.data
  },
}
