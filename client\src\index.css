/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #333;
  background-color: #f5f5f5;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f5f5f5;
}

a {
  color: #667eea;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333;
  font-weight: 600;
  line-height: 1.2;
}

button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
}

input,
select,
textarea {
  font-family: inherit;
}

/* Utility Classes */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.error {
  color: #dc3545;
  text-align: center;
  padding: 2rem;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}
