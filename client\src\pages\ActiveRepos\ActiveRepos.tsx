import React, { useState } from 'react'
import { useRepos } from '@hooks/useRepos'
import { DataTable, type Column } from '@components/DataTable/DataTable'
import { Pagination } from '@components/Pagination/Pagination'
import type { WorkRepo } from '@types'

const ActiveRepos: React.FC = () => {
  const [page, setPage] = useState(1)
  const limit = 50

  const { data, isLoading, error } = useRepos({ page, limit })

  // Define table columns
  const columns: Column<WorkRepo>[] = [
    {
      key: 'action',
      header: 'Actions',
      className: 'action-column',
      render: () => (
        <button className="btn-action btn-view">
          View
        </button>
      )
    },
    {
      key: 'id',
      header: 'ID',
      render: (value) => `#${value}`
    },
    {
      key: 'creator_email',
      header: 'Creator',
      render: (value) => value || 'N/A'
    },
    {
      key: 'repo_type',
      header: 'Type'
    },
    {
      key: 'customer_fault',
      header: 'Customer Fault',
      render: (value) => (
        <span className={`badge ${value ? 'badge-yes' : 'badge-no'}`}>
          {value ? 'Yes' : 'No'}
        </span>
      )
    },
    {
      key: 'units_affected',
      header: 'Units Affected'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
        <p className="text-gray-600">Loading active repositions...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Active Repositions</h2>
        <p className="text-gray-600">Unable to load repositions: {error.message}</p>
      </div>
    )
  }

  const repos = data?.data || []
  const total = data?.total || 0
  const totalPages = Math.ceil(total / limit)

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Active Repositions</h1>
        <p className="text-gray-600">Manufacturing orders currently being processed for rework</p>
      </div>

      <div className="card">
        <DataTable
          data={repos}
          columns={columns}
          keyField="id"
          minWidth="600px"
          emptyMessage="There are currently no active repositions in the system."
        />

        <div className="mt-6">
          <Pagination
            currentPage={page}
            totalPages={totalPages}
            onPageChange={setPage}
            total={total}
            limit={limit}
          />
        </div>
      </div>
    </div>
  )
}

export default ActiveRepos
