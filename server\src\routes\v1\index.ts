import express, { Request, Response } from 'express'
import { getHello } from '@app/controllers/test'
import repoRoutes from './repos'
import materialRoutes from './materials'
import moRoutes from './mos'
import workAreaRoutes from './workAreas'

export const v1Router = express.Router()

v1Router.get('/', (_req: Request, res: Response) => {
  res.send('API v1')
})

v1Router.get('/hello', getHello)

// Repo management routes
v1Router.use('/repos', repoRoutes)

// Material search routes
v1Router.use('/materials', materialRoutes)

// MO routes
v1Router.use('/mos', moRoutes)

// Work area routes
v1Router.use('/work-areas', workAreaRoutes)
