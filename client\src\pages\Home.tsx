import React from 'react'
import { Link } from 'react-router-dom'
import { useHomeStats } from '../hooks/useRepos'
import './Home.css'

const Home: React.FC = () => {
  const { data: stats, isLoading, error } = useHomeStats()

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading repository statistics...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="error">
        <h2>Error Loading Home Page</h2>
        <p>Unable to load repository statistics: {error.message}</p>
      </div>
    )
  }

  return (
    <div className="home">
      <div className="header">
        <h1>Reposition Tracking System</h1>
        <p>Manufacturing Order Rework Management Dashboard</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-number">{stats?.totalRepos || 0}</div>
          <div className="stat-label">Total Repositions</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats?.activeRepos || 0}</div>
          <div className="stat-label">Active Repositions</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats?.completedRepos || 0}</div>
          <div className="stat-label">Completed Repositions</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats?.pendingRepos || 0}</div>
          <div className="stat-label">Pending Repositions</div>
        </div>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/create-repo" className="action-btn primary">
            Create New Reposition
          </Link>
          <Link to="/active-repos" className="action-btn">
            View Active Repositions
          </Link>
          <Link to="/prepare-repos" className="action-btn">
            Prepare Repositions
          </Link>
        </div>
      </div>

      {stats?.recentRepos && stats.recentRepos.length > 0 && (
        <div className="section">
          <h2>Recent Repositions</h2>
          <div className="recent-repos">
            {stats.recentRepos.map((repo) => (
              <div key={repo.id} className="repo-item">
                <div className="repo-id">Reposition #{repo.id}</div>
                <div className="repo-details">
                  {repo.creator_email && `Creator: ${repo.creator_email}`}
                  {repo.creator_email && repo.repo_type && ' | '}
                  {repo.repo_type && `Type: ${repo.repo_type}`}
                  {(repo.creator_email || repo.repo_type) && ' | '}
                  Created: {new Date(repo.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="section">
        <div className="api-links">
          <h3>API Endpoints</h3>
          <a href="/api/v1/repos" target="_blank" rel="noopener noreferrer">
            All Repositions
          </a>
          <a href="/api/v1/repos/statuses" target="_blank" rel="noopener noreferrer">
            Status Definitions
          </a>
          <a href="/api/v1" target="_blank" rel="noopener noreferrer">
            API v1 Info
          </a>
        </div>
      </div>
    </div>
  )
}

export default Home
