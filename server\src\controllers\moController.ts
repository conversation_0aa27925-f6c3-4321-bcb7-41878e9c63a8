import { Request, Response } from 'express'
import { moService } from '../services/moService'

// Search MOs
export const searchMos = async (req: Request, res: Response) => {
  try {
    const { mo_order, customer, num, page = 1, limit = 50 } = req.query
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string)

    const result = await moService.searchMos({
      mo_order: mo_order as string,
      customer: customer as string,
      num: num as string,
      limit: parseInt(limit as string),
      offset,
    })

    res.json(result)
  } catch (error) {
    console.error('Error searching MOs:', error)
    res.status(500).json({ error: 'Failed to search MOs' })
  }
}

// Get available MOs (those without work repos)
export const getAvailableMos = async (req: Request, res: Response) => {
  try {
    const { limit = 50, offset = 0 } = req.query

    const result = await moService.getAvailableMos(
      parseInt(limit as string),
      parseInt(offset as string)
    )

    res.json(result)
  } catch (error) {
    console.error('Error getting available MOs:', error)
    res.status(500).json({ error: 'Failed to get available MOs' })
  }
}

// Get MO by ID
export const getMoById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const mo = await moService.getMoById(parseInt(id))

    if (!mo) {
      res.status(404).json({ error: 'MO not found' })
      return
    }

    res.json(mo)
  } catch (error) {
    console.error('Error getting MO:', error)
    res.status(500).json({ error: 'Failed to get MO' })
  }
}

// Get scans for MO
export const getScansByMoId = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const scans = await moService.getScansByMoId(parseInt(id))

    res.json(scans)
  } catch (error) {
    console.error('Error getting MO scans:', error)
    res.status(500).json({ error: 'Failed to get MO scans' })
  }
}
