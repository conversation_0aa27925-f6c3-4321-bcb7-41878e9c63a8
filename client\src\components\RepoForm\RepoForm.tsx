import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useMo } from '@hooks/useMos'
import { useCreateRepo } from '@hooks/useRepos'
import { useActiveWorkAreas } from '@hooks/useMaterials'
import type { CreateRepoForm } from '../../types'

interface RepoFormProps {
  moId: number
  onBack: () => void
}

export const RepoForm: React.FC<RepoFormProps> = ({ moId, onBack }) => {
  const navigate = useNavigate()
  const { data: mo, isLoading: isLoadingMo } = useMo(moId)
  const { data: workAreas } = useActiveWorkAreas()
  const createRepoMutation = useCreateRepo()

  const [formData, setFormData] = useState<CreateRepoForm>({
    mo_id: moId,
    creator_email: '',
    repo_type: '',
    customer_fault: false,
    units_affected: 0,
    items: '',
    materials: '',
    notes: '',
    printer: '',
    rework_reason: '',
    work_areas: [],
    selected_materials: [],
  })

  const handleInputChange = (field: keyof CreateRepoForm, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleWorkAreaChange = (areaId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      work_areas: checked
        ? [...(prev.work_areas || []), areaId]
        : (prev.work_areas || []).filter(id => id !== areaId)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      await createRepoMutation.mutateAsync(formData)
      navigate('/active-repos')
    } catch (error) {
      console.error('Error creating repo:', error)
    }
  }

  if (isLoadingMo) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
        <p className="text-gray-600">Loading manufacturing order details...</p>
      </div>
    )
  }

  if (!mo) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Manufacturing Order Not Found</h3>
        <p className="text-gray-600 mb-4">The selected manufacturing order could not be found.</p>
        <button onClick={onBack} className="btn-secondary">
          Back to Selection
        </button>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="card">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Selected Manufacturing Order</h2>
          <button onClick={onBack} className="btn-secondary text-sm">
            Change Selection
          </button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <span className="text-sm font-medium text-gray-500">MO ID</span>
            <p className="text-sm text-gray-900">{mo.mo_id}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Order</span>
            <p className="text-sm text-gray-900">{mo.mo_order}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Customer</span>
            <p className="text-sm text-gray-900">{mo.customer}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Number</span>
            <p className="text-sm text-gray-900">{mo.num}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Create Reposition</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-1">
            <label htmlFor="creator_email" className="form-label">Creator Email</label>
            <input
              type="email"
              id="creator_email"
              value={formData.creator_email}
              onChange={(e) => handleInputChange('creator_email', e.target.value)}
              placeholder="Enter creator email"
              className="form-input"
              required
            />
          </div>

          <div className="space-y-1">
            <label htmlFor="repo_type" className="form-label">Reposition Type</label>
            <select
              id="repo_type"
              value={formData.repo_type}
              onChange={(e) => handleInputChange('repo_type', e.target.value)}
              className="form-input"
              required
            >
              <option value="">Select type</option>
              <option value="Corte">Corte</option>
              <option value="Corte Sub">Corte Sub</option>
              <option value="Full Process">Full Process</option>
              <option value="Materials Only">Materials Only</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="customer_fault"
              checked={formData.customer_fault}
              onChange={(e) => handleInputChange('customer_fault', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="customer_fault" className="text-sm font-medium text-gray-700">
              Customer Fault
            </label>
          </div>

          <div className="space-y-1">
            <label htmlFor="units_affected" className="form-label">Units Affected</label>
            <input
              type="number"
              id="units_affected"
              value={formData.units_affected}
              onChange={(e) => handleInputChange('units_affected', parseInt(e.target.value) || 0)}
              min="0"
              className="form-input"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-1">
            <label htmlFor="items" className="form-label">Items</label>
            <textarea
              id="items"
              value={formData.items}
              onChange={(e) => handleInputChange('items', e.target.value)}
              placeholder="Enter items"
              rows={4}
              className="form-input resize-none"
            />
          </div>

          <div className="space-y-1">
            <label htmlFor="materials" className="form-label">Materials</label>
            <textarea
              id="materials"
              value={formData.materials}
              onChange={(e) => handleInputChange('materials', e.target.value)}
              placeholder="Enter materials"
              rows={4}
              className="form-input resize-none"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-1">
            <label htmlFor="printer" className="form-label">Printer</label>
            <input
              type="text"
              id="printer"
              value={formData.printer}
              onChange={(e) => handleInputChange('printer', e.target.value)}
              placeholder="Enter printer"
              className="form-input"
            />
          </div>

          <div className="space-y-1">
            <label htmlFor="rework_reason" className="form-label">Rework Reason</label>
            <input
              type="text"
              id="rework_reason"
              value={formData.rework_reason}
              onChange={(e) => handleInputChange('rework_reason', e.target.value)}
              placeholder="Enter rework reason"
              className="form-input"
            />
          </div>
        </div>

        <div className="space-y-1 mb-6">
          <label htmlFor="notes" className="form-label">Notes</label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Enter notes"
            rows={3}
            className="form-input resize-none"
          />
        </div>

        {workAreas && workAreas.length > 0 && (
          <div className="space-y-3 mb-6">
            <label className="form-label">Work Areas</label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {workAreas.map((area) => (
                <label key={area.id} className="flex items-center space-x-2 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={(formData.work_areas || []).includes(area.id)}
                    onChange={(e) => handleWorkAreaChange(area.id, e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{area.name}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-end pt-6 border-t border-gray-200">
          <button type="button" onClick={onBack} className="btn-secondary">
            Cancel
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={createRepoMutation.isPending}
          >
            {createRepoMutation.isPending ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </div>
            ) : (
              'Create Reposition'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}


