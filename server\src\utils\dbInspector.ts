import { db } from '../db/database'
import { sql } from 'kysely'

interface ColumnInfo {
  Field: string
  Type: string
  Null: string
  Key: string
  Default: string | null
  Extra: string
}

export class DatabaseInspector {
  // Only show columns for a specific table, no other database inspection should happen
  async showColumns(tableName: string): Promise<void> {
    try {
      const result = await sql`SHOW COLUMNS FROM ${sql.id(tableName)}`
        .$castTo<ColumnInfo>()
        .execute(db)

      console.log(`Columns for table ${tableName}:`)
      console.log(
        result.rows
          .map(
            (row: ColumnInfo) =>
              `${row.Field} ${row.Type} (null: ${row.Null}, key: ${row.Key}, default: ${row.Default}, extra: ${row.Extra})`
          )
          .join('\n')
      )
      console.log('\n')
    } catch (error) {
      console.error(`Error showing columns for table ${tableName}:`, error)
      throw error
    }
  }
}

// Create and export an instance
export const dbInspector = new DatabaseInspector()

// CLI function for direct execution
export async function runDatabaseInspection() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2)
    const tableName = args[0]

    if (tableName) {
      // Inspect specific table
      await dbInspector.showColumns(tableName)
    } else {
      // No table specified, show usage or inspect all work_repo tables
      console.log('Usage: npm run db:inspect [table_name]')
      console.log('Examples:')
      console.log('  npm run db:inspect work_repos')
      console.log('  npm run db:inspect work_repo_materials')
      console.log(
        '  npm run db:inspect                    # Inspect all work_repo tables'
      )
      console.log('must supply a table name')
    }
  } catch (error) {
    console.error('Failed to run database inspection:', error)
  } finally {
    // Close the database connection
    await db.destroy()
    process.exit(0)
  }
}

// If this file is run directly, execute the inspection
if (require.main === module) {
  runDatabaseInspection()
}
