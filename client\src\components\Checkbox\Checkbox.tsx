import React from 'react'

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  error?: string
  description?: string
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  error,
  description,
  className = '',
  id,
  ...props
}) => {
  const checkboxClasses = `h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors duration-200 ${
    error ? 'border-danger-300' : ''
  }`
  
  return (
    <div className="flex items-start space-x-2">
      <input
        type="checkbox"
        id={id}
        className={checkboxClasses}
        {...props}
      />
      {label && (
        <div className="flex-1">
          <label htmlFor={id} className="text-sm font-medium text-gray-700 cursor-pointer">
            {label}
          </label>
          {description && (
            <p className="text-xs text-gray-500 mt-1">{description}</p>
          )}
          {error && (
            <p className="text-sm text-danger-600 mt-1">{error}</p>
          )}
        </div>
      )}
    </div>
  )
}

export interface CheckboxGroupProps {
  children: React.ReactNode
  label?: string
  error?: string
  className?: string
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  children,
  label,
  error,
  className = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-semibold text-gray-700 mb-2">
          {label}
        </label>
      )}
      <div className="space-y-2">
        {children}
      </div>
      {error && (
        <p className="text-sm text-danger-600 mt-1">{error}</p>
      )}
    </div>
  )
}
