import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { NavLink } from './NavLink'

interface LayoutProps {
  children: React.ReactNode
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/"
              className="text-xl font-bold text-primary-600 hover:text-primary-700 transition-colors duration-200"
            >
              Reposition Tracker
            </Link>
            <ul className="flex space-x-8">
              <li>
                <NavLink to="/">
                  Home
                </NavLink>
              </li>
              <li>
                <NavLink to="/create-repo">
                  Create Reposition
                </NavLink>
              </li>
              <li>
                <NavLink to="/active-repos">
                  Active Repositions
                </NavLink>
              </li>
              <li>
                <NavLink to="/prepare-repos">
                  Prepare Repositions
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <main className="flex-1">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
    </div>
  )
}


