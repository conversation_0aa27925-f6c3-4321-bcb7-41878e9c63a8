import { apiClient } from '../lib/api'
import type {
  MaterialAllocation,
  MaterialSearchParams,
} from '../types'

export const materialService = {
  // Search materials
  async searchMaterials(params: MaterialSearchParams): Promise<MaterialAllocation[]> {
    const response = await apiClient.get('/v1/materials/search', { params })
    return response.data
  },

  // Get materials for a specific MO
  async getMoMaterials(moId: number, customer?: string): Promise<any[]> {
    const response = await apiClient.get('/v1/materials/mo-materials', {
      params: { mo_id: moId, customer }
    })
    return response.data
  },

  // Get material by part number
  async getMaterialByPartNumber(partNumber: string): Promise<MaterialAllocation> {
    const response = await apiClient.get(`/v1/materials/${partNumber}`)
    return response.data
  },
}
