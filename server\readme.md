## Database

### Migration Commands

To create a migration:

```bash
npx kysely migrate:make table-some_table_name
npx kysely migrate:make column-some_column-some_table_name
```

To run migrations:

```bash
npx kysely migrate:up      # Run a single migration
npx kysely migrate:latest  # Run all unrun migrations
npx kysely migrate:down    # Undo the last migration
```

### Database Schema

The application uses MySQL with tables for:

- `work_repos` - Main repository records
- `mo_numbers` - Manufacturing order information
- `work_repo_materials` - Materials associated with repositories
- `work_repo_pieces` - Pieces/components for repositories
- `work_areas` - Available work areas
- `material_allocations` - Material inventory
