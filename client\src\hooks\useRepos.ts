import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { repoService } from '../services/repoService'
import type { PaginationParams, CreateRepoForm } from '../types'

// Query keys
export const repoKeys = {
  all: ['repos'] as const,
  lists: () => [...repoKeys.all, 'list'] as const,
  list: (params?: PaginationParams) => [...repoKeys.lists(), params] as const,
  details: () => [...repoKeys.all, 'detail'] as const,
  detail: (id: number) => [...repoKeys.details(), id] as const,
  fullDetail: (id: number) => [...repoKeys.details(), id, 'full'] as const,
  statuses: () => [...repoKeys.all, 'statuses'] as const,
  homeStats: () => [...repoKeys.all, 'homeStats'] as const,
  byMoId: (moId: number) => [...repoKeys.all, 'byMoId', moId] as const,
}

// Hooks
export const useRepos = (params?: PaginationParams) => {
  return useQuery({
    queryKey: repoKeys.list(params),
    queryFn: () => repoService.getAllRepos(params),
  })
}

export const useRepo = (id: number) => {
  return useQuery({
    queryKey: repoKeys.detail(id),
    queryFn: () => repoService.getRepoById(id),
    enabled: !!id,
  })
}

export const useRepoWithAllData = (id: number) => {
  return useQuery({
    queryKey: repoKeys.fullDetail(id),
    queryFn: () => repoService.getRepoWithAllData(id),
    enabled: !!id,
  })
}

export const useRepoStatuses = () => {
  return useQuery({
    queryKey: repoKeys.statuses(),
    queryFn: () => repoService.getAllStatuses(),
  })
}

export const useHomeStats = () => {
  return useQuery({
    queryKey: repoKeys.homeStats(),
    queryFn: () => repoService.getHomeStats(),
  })
}

export const useReposByMoId = (moId: number) => {
  return useQuery({
    queryKey: repoKeys.byMoId(moId),
    queryFn: () => repoService.getReposByMoId(moId),
    enabled: !!moId,
  })
}

// Mutations
export const useCreateRepo = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateRepoForm) => repoService.createRepo(data),
    onSuccess: () => {
      // Invalidate and refetch repos list
      queryClient.invalidateQueries({ queryKey: repoKeys.lists() })
      queryClient.invalidateQueries({ queryKey: repoKeys.homeStats() })
    },
  })
}

export const useUpdateRepo = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<CreateRepoForm> }) =>
      repoService.updateRepo(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch specific repo and lists
      queryClient.invalidateQueries({ queryKey: repoKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: repoKeys.fullDetail(id) })
      queryClient.invalidateQueries({ queryKey: repoKeys.lists() })
    },
  })
}

export const useDeleteRepo = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => repoService.deleteRepo(id),
    onSuccess: () => {
      // Invalidate and refetch repos list
      queryClient.invalidateQueries({ queryKey: repoKeys.lists() })
      queryClient.invalidateQueries({ queryKey: repoKeys.homeStats() })
    },
  })
}
