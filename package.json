{"name": "repo-tracking-system", "version": "0.1.9", "main": "index.js", "scripts": {"build": "npm run build:server && npm run build:client", "build:server": "cd server && npm run build", "build:client": "cd client && npm run build", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:full": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "install:all": "npm install && npm run install:server && npm run install:client", "install:server": "cd server && npm install", "install:client": "cd client && npm install", "start": "cd server && npm start", "db:inspect": "cd server && npm run db:inspect", "db:query": "cd server && npm run db:query"}, "keywords": [], "author": "", "license": "ISC", "description": "Repository tracking system with React frontend and Node.js backend", "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {}}