import { ColumnType, Generated } from 'kysely'

// MO Sc<PERSON> table
export interface MoScansTable {
  scan_id: Generated<number>
  mo_id: number
  sew_ready: ColumnType<Date, string | undefined, string | undefined> | null
  sew: ColumnType<Date, string | undefined, string | undefined> | null
  received_at: ColumnType<Date, string | undefined, string | undefined> | null
  closed_at: ColumnType<Date, string | undefined, string | undefined> | null
  start_utc: ColumnType<Date, string | undefined, string | undefined> | null
  finish_utc: ColumnType<Date, string | undefined, string | undefined> | null
  is_from_scan: boolean
  is_manual_change: boolean
  is_repo: boolean
  supervisor: string
  supervisor_code: number | null
  task_name: string | null
  quantity: number | null
  work_area_id: number | null
  work_area_group_id: number | null
  work_voucher_id: number | null
  work_area_ticket_id: number | null
  work_area_line_id: number | null
  work_area_group_shift_id: number | null
  poly_status: number | null
  varsity_status: number
  employee_id: number | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
  removed_at: ColumnType<Date, string | undefined, string | undefined> | null
  style_sam_id: number | null
  style_sam_group_id: number | null
  sew_sam_value: number | null
  work_repo_id: number | null
  work_fragment_id: number | null
  merged_scan_id: number | null
  deleted_by_merge: boolean | null
}
