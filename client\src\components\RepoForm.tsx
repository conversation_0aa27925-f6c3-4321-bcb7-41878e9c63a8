import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useMo } from '../hooks/useMos'
import { useCreateRepo } from '../hooks/useRepos'
import { useActiveWorkAreas } from '../hooks/useMaterials'
import type { CreateRepoForm } from '../types'
import './RepoForm.css'

interface RepoFormProps {
  moId: number
  onBack: () => void
}

const RepoForm: React.FC<RepoFormProps> = ({ moId, onBack }) => {
  const navigate = useNavigate()
  const { data: mo, isLoading: isLoadingMo } = useMo(moId)
  const { data: workAreas } = useActiveWorkAreas()
  const createRepoMutation = useCreateRepo()

  const [formData, setFormData] = useState<CreateRepoForm>({
    mo_id: moId,
    creator_email: '',
    repo_type: '',
    customer_fault: false,
    units_affected: 0,
    items: '',
    materials: '',
    notes: '',
    printer: '',
    rework_reason: '',
    work_areas: [],
    selected_materials: [],
  })

  const handleInputChange = (field: keyof CreateRepoForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleWorkAreaChange = (areaId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      work_areas: checked
        ? [...(prev.work_areas || []), areaId]
        : (prev.work_areas || []).filter(id => id !== areaId)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      await createRepoMutation.mutateAsync(formData)
      navigate('/active-repos')
    } catch (error) {
      console.error('Error creating repo:', error)
    }
  }

  if (isLoadingMo) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading manufacturing order details...</p>
      </div>
    )
  }

  if (!mo) {
    return (
      <div className="error">
        <h3>Manufacturing Order Not Found</h3>
        <p>The selected manufacturing order could not be found.</p>
        <button onClick={onBack} className="btn-back">
          Back to Selection
        </button>
      </div>
    )
  }

  return (
    <div className="repo-form">
      <div className="mo-info">
        <h2>Selected Manufacturing Order</h2>
        <div className="mo-details">
          <div className="detail-item">
            <strong>MO ID:</strong> {mo.mo_id}
          </div>
          <div className="detail-item">
            <strong>Order:</strong> {mo.mo_order}
          </div>
          <div className="detail-item">
            <strong>Customer:</strong> {mo.customer}
          </div>
          <div className="detail-item">
            <strong>Number:</strong> {mo.num}
          </div>
        </div>
        <button onClick={onBack} className="btn-back">
          Change Selection
        </button>
      </div>

      <form onSubmit={handleSubmit} className="form">
        <h2>Create Reposition</h2>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="creator_email">Creator Email</label>
            <input
              type="email"
              id="creator_email"
              value={formData.creator_email}
              onChange={(e) => handleInputChange('creator_email', e.target.value)}
              placeholder="Enter creator email"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="repo_type">Reposition Type</label>
            <select
              id="repo_type"
              value={formData.repo_type}
              onChange={(e) => handleInputChange('repo_type', e.target.value)}
            >
              <option value="">Select type</option>
              <option value="Corte">Corte</option>
              <option value="Corte Sub">Corte Sub</option>
              <option value="Full Process">Full Process</option>
              <option value="Materials Only">Materials Only</option>
            </select>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={formData.customer_fault}
                onChange={(e) => handleInputChange('customer_fault', e.target.checked)}
              />
              Customer Fault
            </label>
          </div>
          
          <div className="form-group">
            <label htmlFor="units_affected">Units Affected</label>
            <input
              type="number"
              id="units_affected"
              value={formData.units_affected}
              onChange={(e) => handleInputChange('units_affected', parseInt(e.target.value) || 0)}
              min="0"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="items">Items</label>
            <textarea
              id="items"
              value={formData.items}
              onChange={(e) => handleInputChange('items', e.target.value)}
              placeholder="Enter items"
              rows={4}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="materials">Materials</label>
            <textarea
              id="materials"
              value={formData.materials}
              onChange={(e) => handleInputChange('materials', e.target.value)}
              placeholder="Enter materials"
              rows={4}
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="printer">Printer</label>
            <input
              type="text"
              id="printer"
              value={formData.printer}
              onChange={(e) => handleInputChange('printer', e.target.value)}
              placeholder="Enter printer"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="rework_reason">Rework Reason</label>
            <input
              type="text"
              id="rework_reason"
              value={formData.rework_reason}
              onChange={(e) => handleInputChange('rework_reason', e.target.value)}
              placeholder="Enter rework reason"
            />
          </div>
        </div>

        <div className="form-group full-width">
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Enter notes"
            rows={3}
          />
        </div>

        {workAreas && workAreas.length > 0 && (
          <div className="form-group full-width">
            <label>Work Areas</label>
            <div className="work-areas-grid">
              {workAreas.map((area) => (
                <label key={area.id} className="work-area-item">
                  <input
                    type="checkbox"
                    checked={(formData.work_areas || []).includes(area.id)}
                    onChange={(e) => handleWorkAreaChange(area.id, e.target.checked)}
                  />
                  {area.name}
                </label>
              ))}
            </div>
          </div>
        )}

        <div className="form-actions">
          <button type="button" onClick={onBack} className="btn-cancel">
            Cancel
          </button>
          <button 
            type="submit" 
            className="btn-submit"
            disabled={createRepoMutation.isPending}
          >
            {createRepoMutation.isPending ? 'Creating...' : 'Create Reposition'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default RepoForm
