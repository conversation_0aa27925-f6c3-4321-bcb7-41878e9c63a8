// Repository Types
export interface WorkRepo {
  id: number
  mo_id: number
  creator_email?: string
  repo_type?: string
  customer_fault?: boolean
  units_affected?: number
  items?: string
  materials?: string
  notes?: string
  printer?: string
  rework_reason?: string
  created_at: string
  updated_at: string
}

export interface WorkRepoMaterial {
  id: number
  work_repo_id: number
  part_number: string
  quantity: number
  units?: string
  created_at: string
  updated_at: string
}

export interface WorkRepoPiece {
  id: number
  work_repo_id: number
  piece_name: string
  quantity: number
  created_at: string
  updated_at: string
}

export interface WorkRepoStatus {
  id: number
  name: string
  description?: string
  color?: string
  created_at: string
  updated_at: string
}

export interface WorkRepoNote {
  id: number
  work_repo_id: number
  note: string
  created_by?: string
  created_at: string
  updated_at: string
}

// MO Types
export interface MoNumber {
  mo_id: number
  mo_order: string
  customer: string
  num: string
  created_at: string
  updated_at: string
}

export interface MoScan {
  id: number
  mo_id: number
  scan_data: string
  scanned_at: string
  created_at: string
  updated_at: string
}

// Work Area Types
export interface WorkArea {
  id: number
  name: string
  description?: string
  active: boolean
  created_at: string
  updated_at: string
}

// Material Types
export interface MaterialAllocation {
  id: number
  part_number: string
  unit_symbol: string
  description?: string
  created_at: string
  updated_at: string
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  total?: number
  page?: number
  limit?: number
  totalPages?: number
  hasMore?: boolean
}

export interface PaginationParams {
  page?: number
  limit?: number
}

export interface RepoSearchParams extends PaginationParams {
  mo_order?: string
  customer?: string
  num?: string
}

export interface MaterialSearchParams {
  query: string
  customer?: string
  limit?: number
}

// Home Page Statistics
export interface HomeStats {
  totalRepos: number
  activeRepos: number
  completedRepos: number
  pendingRepos: number
  recentRepos: WorkRepo[]
}

// Full Repo Data (with all related data)
export interface FullRepoData {
  repo: WorkRepo
  mo: MoNumber
  materials: WorkRepoMaterial[]
  pieces: WorkRepoPiece[]
  notes: WorkRepoNote[]
  statuses: WorkRepoStatus[]
  scans: MoScan[]
}

// Form Types
export interface CreateRepoForm {
  mo_id: number
  creator_email?: string
  repo_type?: string
  customer_fault?: boolean
  units_affected?: number
  items?: string
  materials?: string
  notes?: string
  printer?: string
  rework_reason?: string
  work_areas?: number[]
  selected_materials?: Array<{
    part_number: string
    quantity: number
    units?: string
  }>
}

// Repository Summary Type
export interface RepoSummary {
  id: number
  mo_id: number
  total_materials: number
  total_pieces: number
  total_notes: number
  estimated_cost?: number
  status?: string
  created_at: string
  updated_at: string
}

// MO Material Type (for poly_restful service)
export interface MoMaterial {
  ManufactureNumber: string
  PartNumber: string
  Description: string
  CategoryName: string
  SubcategoryName: string
  DatabaseUnits: string
  QuantityRequired: string
  QuantityWithdrawn: string
  QuantityOnHand: string
}

// Database Query Result Type
export interface QueryResult {
  rows: Record<string, unknown>[]
  rowCount?: number
  command?: string
}

// Generic value type for utility functions
export type PrimitiveValue = string | number | boolean | null | undefined
